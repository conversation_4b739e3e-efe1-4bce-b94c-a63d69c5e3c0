import React from 'react';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
import type { Review } from '@/lib/types';

// Import the CSS that contains adaptive-text-title styles
import '@/components/review-form/style/NewReview.css';

interface GameInfoCardProps {
  review?: Review;
}

const GameInfoCard: React.FC<GameInfoCardProps> = ({ review }) => {
  const isDarkBackground = useBackgroundBrightness();
  
  if (!review) return null;
  
  // Format datePlayed field correctly - handle MM/YYYY format
  const formatDatePlayed = (dateValue?: any): string => {
    if (!dateValue) return 'Not specified';

    try {
      // If it's already MM/YYYY format, return it
      if (typeof dateValue === 'string' && /^\d{1,2}\/\d{4}$/.test(dateValue)) {
        const [month, year] = dateValue.split('/');
        return `${month.padStart(2, '0')}/${year}`;
      }

      // Parse any other date format and convert to MM/YYYY
      const date = new Date(dateValue);
      if (!isNaN(date.getTime())) {
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${month}/${year}`;
      }
    } catch (error) {
      console.warn('Error formatting date played:', error);
    }

    return 'Not specified';
  };
  
  return (
    <div className="mb-6 w-full max-w-full 2xl:max-w-[75%] mx-auto">
      {/* Game Name - no header */}
      <div className="text-left mb-1">
        <div className={`text-sm font-mono uppercase tracking-wider adaptive-text-content ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
          {review.gameName || 'Unknown Game'}
        </div>
      </div>
      
      {/* Review Title - no header */}
      <div className="text-left mb-2">
        <h1 className={`text-3xl md:text-4xl font-bold font-mono leading-tight adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
          <span className="text-violet-400 mr-2">//</span>{review.title || 'Untitled Review'}
        </h1>
      </div>

      {/* Game Information Strip */}
      <div className="text-left mt-1 space-y-1">
        {/* Platform */}
        <div className="flex items-center">
          <span className={`text-sm font-mono mr-2 adaptive-text-content ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
            Played on:
          </span>
          <span className={`text-sm font-mono adaptive-text-content ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
            {review.playedOn || 'Not specified'}
          </span>
        </div>
        
        {/* Date Played */}
        <div className="flex items-center">
          <span className={`text-sm font-mono mr-2 adaptive-text-content ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
            Date Played:
          </span>
          <span className={`text-sm font-mono adaptive-text-content ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
            {formatDatePlayed(review.datePlayed)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default GameInfoCard;
