'use client';

import { useState, useEffect, useRef, KeyboardEvent, useMemo, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Gamepad2,
  Edit3,
  Save,
  X,
  Plus,
  ArrowRight,
  FileText,
  Hash,
  AlertCircle,
  CheckCircle2
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { GameSearchCombobox } from '@/components/review-form/igdbsearch';
import PerformanceSurvey, { PerformanceSurveyData } from '@/components/review-form/performanceSurvey';
import { savePerformanceSurvey, hasUserSubmittedSurveyForGame } from '@/lib/services/performanceSurveyService';
import { useAuthContext } from '@/hooks/use-auth-context';
import EnhancedTagInput from '@/components/review-form/EnhancedTagInput';
import { useDebounce } from '@/lib/hooks/useDebounce';
import '@/components/review-form/style/newReviewFlow.css';

// Game interface for the search component
interface Game {
  id: string;
  name: string;
  cover?: {
    id: string;
    url: string;
  };
  releaseDate?: number;
  platforms?: string[];
  genres?: string[];
  developers?: {
    id: string;
    name: string;
  }[];
  rating?: number;
  summary?: string;
  total_rating?: number;
  screenshots?: {
    id: string;
    url: string;
  }[];
  aggregated_rating?: number;
  aggregated_rating_count?: number;
  first_release_date?: number;
  involved_companies?: {
    id: string;
    company: {
      id: string;
      name: string;
    };
    developer: boolean;
    publisher: boolean;
  }[];
  time_to_beat?: {
    completely?: number;
    normally?: number;
  };
  game_engines?: string[];
  player_perspectives?: string[];
  cpx_score?: number;
}

// IGDB Game interface with proper metadata structure
interface IGDBGame extends Game {
  // Additional IGDB-specific properties can be added here
}

// Enhanced Flow steps enum - NOW 4 STEPS
enum FlowStep {
  GAME_SEARCH = 0,
  REVIEW_DETAILS = 1,
  FINAL_SUMMARY = 2
}

// Constants
const defaultPlatforms = [
  "PC", "PlayStation 5", "PlayStation 4", "Xbox Series X/S", "Xbox One", 
  "Nintendo Switch", "iOS", "Android", "macOS", "Linux"
];

const languages = [
  { code: "en", name: "English" },
  { code: "es", name: "Spanish" },
  { code: "fr", name: "French" },
  { code: "de", name: "German" },
  { code: "it", name: "Italian" },
  { code: "pt", name: "Portuguese" },
  { code: "ru", name: "Russian" },
  { code: "ja", name: "Japanese" },
  { code: "ko", name: "Korean" },
  { code: "zh", name: "Chinese" }
];

const popularTags = [
  "action", "adventure", "rpg", "indie", "strategy", 
  "souls-like", "open-world", "puzzle", "horror", "platformer",
  "racing", "simulation", "story-rich", "multiplayer", "sandbox",
  "casual", "pixel-art", "roguelike", "fps", "good-game"
];

// Corrected tag validation - max 18 characters, optional prefix-suffix
const normalizeTag = (input: string): string => {
  // Convert to lowercase and replace spaces/special chars with hyphens
  return input
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .replace(/-+/g, '-'); // Replace multiple hyphens with single
};

const isValidTagFormat = (tag: string): boolean => {
  if (!tag || tag.length === 0) return false;
  if (tag.length > 18) return false;
  
  // After normalization, should only contain lowercase letters, numbers, and hyphens
  const normalized = normalizeTag(tag);
  return /^[a-z0-9-]+$/.test(normalized) && normalized.length <= 18;
};

const getTagValidationError = (tag: string): string | null => {
  if (!tag) return null;
  
  if (tag.length > 18) {
    return `Maximum 18 characters (currently ${tag.length})`;
  }
  
  const normalized = normalizeTag(tag);
  if (normalized.length === 0) {
    return "Tag cannot be empty after normalization";
  }
  
  if (normalized.length > 18) {
    return `Normalized tag too long: "${normalized}" (${normalized.length} chars)`;
  }
  
  return null;
};

interface TitleYourReviewProps {
  reviewTitle: string;
  setReviewTitle: (value: string) => void;
  gameName: string;
  setGameName: (value: string) => void;
  initialAvailablePlatforms: string[];
  igdbId?: number;
  igdbSummary: string;
  setIgdbSummary: (value: string) => void;
  igdbAggregatedRating: number | undefined;
  setIgdbAggregatedRating: (value: number | undefined) => void;
  igdbAggregatedRatingCount: number | undefined;
  setIgdbAggregatedRatingCount: (value: number | undefined) => void;
  initialAvailableGenres: string[];
  language: string;
  setLanguage: (value: string) => void;
  playedOn: string;
  setPlayedOn: (value: string) => void;
  datePlayed: string;
  setDatePlayed: (value: string) => void;
  setIgdbDevelopers: (value: string[]) => void;
  setIgdbPublishers: (value: string[]) => void;
  setIgdbGameEngines: (value: string[]) => void;
  setIgdbPlayerPerspectives: (value: string[]) => void;
  setIgdbTimeToBeatNormally: (value: number | undefined) => void;
  setIgdbTimeToBeatCompletely: (value: number | undefined) => void;
  setMainImageUrl: (url: string) => void;
  setIgdbCoverUrl: (url: string) => void;
  setIgdbId: (id: number | undefined) => void;
  setReleaseDate: (date: Date | undefined) => void;
  onTagsChange?: (tags: string[]) => void;
  currentTags?: string[]; // Current tags from parent
  selectedPlatforms: Set<string>;
  onPlatformToggle: (platform: string) => void;
  selectedGenres: Set<string>;
  onGenreToggle: (genre: string) => void;
  isEditMode?: boolean;
}

// Utility functions
const formatDate = (timestamp?: number) => {
  if (!timestamp) return 'Unknown';
  
  // Handle both Unix timestamp (seconds) and JavaScript timestamp (milliseconds)
  const date = timestamp > 1000000000000 
    ? new Date(timestamp) 
    : new Date(timestamp * 1000);
    
  return date.toLocaleDateString(undefined, { year: 'numeric', month: 'short' });
};

const TitleYourReview: React.FC<TitleYourReviewProps> = ({
  reviewTitle,
  setReviewTitle,
  gameName,
  setGameName,
  initialAvailablePlatforms = defaultPlatforms,
  igdbId,
  igdbSummary,
  setIgdbSummary,
  igdbAggregatedRating,
  setIgdbAggregatedRating,
  igdbAggregatedRatingCount,
  setIgdbAggregatedRatingCount,
  language,
  setLanguage,
  playedOn,
  setPlayedOn,
  datePlayed,
  setDatePlayed,
  isEditMode = false,
  initialAvailableGenres,
  setIgdbDevelopers,
  setIgdbPublishers,
  setIgdbGameEngines,
  setIgdbPlayerPerspectives,
  setIgdbTimeToBeatNormally,
  setIgdbTimeToBeatCompletely,
  setMainImageUrl,
  setIgdbCoverUrl,
  setIgdbId,
  setReleaseDate,
  onTagsChange = () => {},
  currentTags = [],
  selectedPlatforms,
  onPlatformToggle,
  selectedGenres,
  onGenreToggle,
}) => {
  // Auth context
  const { user } = useAuthContext();

  // Flow state
  const [currentStep, setCurrentStep] = useState<FlowStep>(FlowStep.GAME_SEARCH);

  // Step interaction tracking - tracks when user first meaningfully interacts with each step
  const [stepInteractions, setStepInteractions] = useState<boolean[]>([false, false, false]);

  // Track highest step reached to allow navigation back to previously visited steps
  const [highestStepReached, setHighestStepReached] = useState<FlowStep>(FlowStep.GAME_SEARCH);
  const [editingField, setEditingField] = useState<string | null>(null);
  
  // Game state
  const [selectedGame, setSelectedGame] = useState<IGDBGame | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [expandSummary, setExpandSummary] = useState<boolean>(false);
  const [gameToLoad, setGameToLoad] = useState<Game | null>(null);
  const [selectedGameId, setSelectedGameId] = useState<string | null>(null);
  
  // Enhanced Tags state
  const [tagInput, setTagInput] = useState<string>('');
  const [tags, setTags] = useState<string[]>([]);
  const [tagError, setTagError] = useState<string>('');
  const [isValidating, setIsValidating] = useState<boolean>(false);
  
  // Debounced validation
  const debouncedTagInput = useDebounce(tagInput, 300);
  
  // Pending updates state (to fix the render issue)
  const [pendingPlatforms, setPendingPlatforms] = useState<string[]>([]);
  const [pendingGenres, setPendingGenres] = useState<string[]>([]);

  // Performance survey state
  const [showPerformanceSurvey, setShowPerformanceSurvey] = useState<boolean>(false);
  const [performanceSurveyData, setPerformanceSurveyData] = useState<PerformanceSurveyData | null>(null);
  const [performanceSurveySkipped, setPerformanceSurveySkipped] = useState<boolean>(false);
  const [hasExistingSurvey, setHasExistingSurvey] = useState<boolean>(false);
  const [checkingSurveyStatus, setCheckingSurveyStatus] = useState<boolean>(false);

  // Check if user has already submitted a survey for this game
  useEffect(() => {
    const checkExistingSurvey = async () => {
      if (user && selectedGame?.name) {
        setCheckingSurveyStatus(true);
        try {
          const result = await hasUserSubmittedSurveyForGame(user.uid, selectedGame.name);
          if (result.success) {
            setHasExistingSurvey(result.hasSubmitted || false);
          }
        } catch (error) {
          console.error('Error checking existing survey:', error);
        } finally {
          setCheckingSurveyStatus(false);
        }
      } else {
        setHasExistingSurvey(false);
      }
    };

    checkExistingSurvey();
  }, [user, selectedGame?.name]);

  // Helper function to check if platform supports performance survey
  const shouldShowPerformanceSurveyButton = useCallback((platform: string) => {
    return platform === 'PC' || platform === 'Linux';
  }, []);

  // Refs
  const tagInputRef = useRef<HTMLInputElement>(null);

  // Computed values
  const playedOnPlatforms = useMemo(() =>
    selectedGame?.platforms || initialAvailablePlatforms,
    [selectedGame?.platforms, initialAvailablePlatforms]
  );

  const getTagSuggestion = useMemo(() => {
    if (!tagInput || tagInput.length < 2) return null;

    const availableTags = popularTags.filter((tag: string) =>
      !tags.includes(normalizeTag(tag))
    );

    const matchingTag = availableTags.find(tag =>
      tag.toLowerCase().startsWith(tagInput.toLowerCase())
    );

    return matchingTag || null;
  }, [tagInput, tags]);

  // Enhanced step completion logic
  const stepCompletion = useMemo(() => {
    return [
      !!selectedGame,                                               // Step 0: Game selected
      !!(reviewTitle && language && playedOn),                    // Step 1: Review details completed (tags optional)
      !!(selectedGame && reviewTitle && language && playedOn)     // Step 2: All requirements met
    ];
  }, [selectedGame, reviewTitle, language, playedOn]);

  // Step status - determines visual state of each step
  const stepStatus = useMemo(() => {
    return [0, 1, 2].map(step => {
      if (stepCompletion[step]) return 'completed';
      if (step === currentStep) return 'active';
      if (step <= highestStepReached) return 'visited';
      return 'upcoming';
    });
  }, [stepCompletion, currentStep, highestStepReached]);

  // Real-time tag validation effect
  useEffect(() => {
    if (debouncedTagInput && debouncedTagInput.length > 0) {
      setIsValidating(true);
      const error = getTagValidationError(debouncedTagInput);
      setTagError(error || '');
      setIsValidating(false);
    } else {
      setTagError('');
      setIsValidating(false);
    }
  }, [debouncedTagInput]);

  // No longer needed - using native autocomplete

  // Separate effect to handle pending platform/genre updates
  useEffect(() => {
    if (pendingPlatforms.length > 0) {
      const timer = setTimeout(() => {
        pendingPlatforms.forEach(platform => {
          onPlatformToggle(platform);
        });
        setPendingPlatforms([]);
      }, 0);
      return () => clearTimeout(timer);
    }
  }, [pendingPlatforms, onPlatformToggle]);

  useEffect(() => {
    if (pendingGenres.length > 0) {
      const timer = setTimeout(() => {
        pendingGenres.forEach(genre => {
          onGenreToggle(genre);
        });
        setPendingGenres([]);
      }, 0);
      return () => clearTimeout(timer);
    }
  }, [pendingGenres, onGenreToggle]);

  const getDevelopers = useCallback((gameData: IGDBGame) => {
    if (gameData.developers && gameData.developers.length > 0) {
      return gameData.developers.map(dev => dev.name);
    }
    if (gameData.involved_companies) {
      return gameData.involved_companies
        .filter(company => company.developer)
        .map(company => company.company.name);
    }
    return [];
  }, []);

  const getPublishers = useCallback((gameData: IGDBGame) => {
    if (gameData.involved_companies) {
      return gameData.involved_companies
        .filter(company => company.publisher)
        .map(company => company.company.name);
    }
    return [];
  }, []);

  // Handle game selection - just set the ID to trigger the effect
  const handleGameSelect = useCallback((game: Game | null) => {
    if (!game) {
      setSelectedGameId(null);
      setGameToLoad(null);
      // Immediately clear the gameName when game is deselected
      setGameName('');
      return;
    }
    setSelectedGameId(game.id);
    setGameToLoad(game);
  }, [setGameName]);

  // Handle the actual data loading in a useEffect
  useEffect(() => {
    const loadGameData = async () => {
      if (!selectedGameId || !gameToLoad) {
        // In edit mode, don't clear the data - it's being loaded from the review
        if (isEditMode) {
          console.log('[TitleYourQuest] Skipping data clearing in edit mode');
          setLoading(false);
          return;
        }
        
        // Clear state if no game is selected (only in create mode)
        setSelectedGame(null);
        setGameName('');
        // Note: mainImageUrl (banner) is not cleared - user controls this independently
        setIgdbCoverUrl(''); // Clear IGDB cover URL
        setIgdbId(undefined);
        setIgdbSummary('');
        setIgdbDevelopers([]);
        setIgdbPublishers([]);
        setIgdbGameEngines([]);
        setIgdbPlayerPerspectives([]);
        setIgdbTimeToBeatNormally(undefined);
        setIgdbTimeToBeatCompletely(undefined);
        setIgdbAggregatedRating(undefined);
        setIgdbAggregatedRatingCount(undefined);
        setReleaseDate(undefined);
        setLoading(false);
        return;
      }

      setLoading(true);
      
      try {
        // First, fetch fresh IGDB data
        const igdbResponse = await fetch(`/api/igdb/game/${selectedGameId}`);
        if (!igdbResponse.ok) {
          throw new Error(`Failed to fetch game data: ${igdbResponse.statusText}`);
        }
        
        const igdbGameData: IGDBGame = await igdbResponse.json();
        
        // Now store/update in our database via the games API
        console.log('[TitleYourQuest] Storing game in database:', selectedGameId, igdbGameData.name);
        const dbResponse = await fetch(`/api/games/${selectedGameId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(igdbGameData)
        });
        
        if (!dbResponse.ok) {
          const errorText = await dbResponse.text();
          console.error('[TitleYourQuest] Failed to store game in database:', dbResponse.status, errorText);
        } else {
          const storedGame = await dbResponse.json();
          console.log('[TitleYourQuest] Game successfully stored in database:', storedGame.name);
        }
        
        // Use the IGDB data for the component (we could also fetch from DB here)
        const fullGameData: IGDBGame = igdbGameData;
        setSelectedGame(fullGameData);
        setGameName(gameToLoad.name);
        
        // Note: Cover image is no longer automatically set as banner image
        // Users must manually set their banner image in the Media & Visuals step

        // Set IGDB cover URL for score overlay display
        if (fullGameData.cover?.url) {
          setIgdbCoverUrl(fullGameData.cover.url.replace('t_thumb', 't_cover_big'));
        }

        // Set IGDB ID and summary
        setIgdbId(parseInt(selectedGameId));
        if (fullGameData.summary) {
          setIgdbSummary(fullGameData.summary);
        }
        
        // Set developers and publishers
        const developers = getDevelopers(fullGameData);
        const publishers = getPublishers(fullGameData);
        setIgdbDevelopers(developers);
        setIgdbPublishers(publishers);
        
        // Set game engines and player perspectives if available
        if (fullGameData.game_engines) {
          setIgdbGameEngines(fullGameData.game_engines);
        }
        
        if (fullGameData.player_perspectives) {
          setIgdbPlayerPerspectives(fullGameData.player_perspectives);
        }
        
        // Set time to beat if available
        if (fullGameData.time_to_beat) {
          setIgdbTimeToBeatNormally(fullGameData.time_to_beat.normally);
          setIgdbTimeToBeatCompletely(fullGameData.time_to_beat.completely);
        }
        
        // Set aggregated rating and count if available
        if (fullGameData.aggregated_rating !== undefined) {
          setIgdbAggregatedRating(fullGameData.aggregated_rating);
        }
        
        if (fullGameData.aggregated_rating_count !== undefined) {
          setIgdbAggregatedRatingCount(fullGameData.aggregated_rating_count);
        }
        
        // Set release date if available
        const releaseDateToSet = fullGameData.releaseDate || fullGameData.first_release_date;
        if (releaseDateToSet) {
          const releaseDate = new Date(releaseDateToSet * 1000);
          setReleaseDate(releaseDate);
        } else {
          setReleaseDate(undefined);
        }

        // Auto-select platform if not set
        if (!playedOn && fullGameData.platforms && fullGameData.platforms.length > 0) {
          const firstPlatform = typeof fullGameData.platforms[0] === 'string' 
            ? fullGameData.platforms[0] 
            : fullGameData.platforms[0]?.name || fullGameData.platforms[0]?.toString() || null;
          if (firstPlatform) {
            setPlayedOn(firstPlatform);
          }
        }

        // Debug: Log the platform and genre data structure
        console.log('[TitleYourQuest] Platform data:', {
          hasPlatforms: !!fullGameData.platforms,
          platformsLength: fullGameData.platforms?.length,
          platformsData: fullGameData.platforms,
          hasGenres: !!fullGameData.genres,
          genresLength: fullGameData.genres?.length,
          genresData: fullGameData.genres
        });

        // Schedule platform and genre updates for the next tick to avoid render issues
        if (fullGameData.platforms && fullGameData.platforms.length > 0) {
          const platformNames = fullGameData.platforms.map(p => 
            typeof p === 'string' ? p : (p?.name || p?.toString() || null)
          ).filter(Boolean);
          console.log('[TitleYourQuest] Setting pending platforms:', platformNames);
          setPendingPlatforms(platformNames);
        }

        if (fullGameData.genres && fullGameData.genres.length > 0) {
          const genreNames = fullGameData.genres.map(g => 
            typeof g === 'string' ? g : (g?.name || g?.toString() || null)
          ).filter(Boolean);
          console.log('[TitleYourQuest] Setting pending genres:', genreNames);
          setPendingGenres(genreNames);
        }
        
        // Auto-fill datePlayed with release info
        if (!datePlayed && releaseDateToSet) {
          const release_date_obj = new Date(releaseDateToSet * 1000);
          const month = String(release_date_obj.getMonth() + 1).padStart(2, '0');
          const year = release_date_obj.getFullYear();
          setDatePlayed(`${month}/${year}`);
        }

        console.log('[TitleYourQuest] Game data loaded:', fullGameData.name);
      } catch (error) {
        console.error('[TitleYourReview] Error loading game data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadGameData();
  }, [
    selectedGameId, 
    gameToLoad, 
    currentStep, 
    datePlayed, 
    playedOn, 
    getDevelopers, 
    getPublishers, 
    onTagsChange,
    setGameName,
    setMainImageUrl,
    setIgdbCoverUrl,
    setIgdbId,
    setIgdbSummary,
    setIgdbDevelopers,
    setIgdbPublishers,
    setIgdbGameEngines,
    setIgdbPlayerPerspectives,
    setIgdbTimeToBeatNormally,
    setIgdbTimeToBeatCompletely,
    setIgdbAggregatedRating,
    setIgdbAggregatedRatingCount,
    setPlayedOn,
    setDatePlayed,
    setReleaseDate
  ]);

  const handleDateInput = useCallback((input: string) => {
    let numbers = input.replace(/[^\d]/g, '');
    if (numbers.length > 6) numbers = numbers.slice(0, 6);
    let formatted = '';
    if (numbers.length > 2) {
      formatted = `${numbers.slice(0, 2)}/${numbers.slice(2)}`;
    } else {
      formatted = numbers;
    }
    setDatePlayed(formatted);
  }, [setDatePlayed]);
  
  // Enhanced tag handling with corrected validation
  const addTag = useCallback((tag: string) => {
    const normalizedTag = normalizeTag(tag.trim());

    if (tags.length >= 8) {
      setTagError('Maximum 8 tags allowed');
      setTimeout(() => setTagError(''), 3000);
      return;
    }

    if (!normalizedTag) {
      setTagError('Tag cannot be empty');
      setTimeout(() => setTagError(''), 3000);
      return;
    }

    if (tags.includes(normalizedTag)) {
      setTagError('Tag already exists');
      setTimeout(() => setTagError(''), 3000);
      return;
    }

    const validationError = getTagValidationError(tag);
    if (validationError) {
      setTagError(validationError);
      setTimeout(() => setTagError(''), 3000);
      return;
    }

    const newTags = [...tags, normalizedTag];
    setTags(newTags);
    if (onTagsChange) {
      onTagsChange(newTags);
    }
    setTagInput('');
    setTagError('');
  }, [tags, onTagsChange]);
  
  const removeTag = useCallback((tagToRemove: string) => {
    const newTags = tags.filter(tag => tag !== tagToRemove);
    setTags(newTags);
    if (onTagsChange) {
      onTagsChange(newTags);
    }
    setTagError('');
  }, [tags, onTagsChange]);
  
  const handleTagKeyDown = useCallback((e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (tagInput) {
        addTag(tagInput);
      }
    } else if (e.key === 'Tab' && getTagSuggestion && tagInput.length >= 2) {
      e.preventDefault();
      setTagInput(getTagSuggestion);
    } else if (e.key === 'Backspace' && !tagInput && tags.length > 0) {
      removeTag(tags[tags.length - 1]);
    }
  }, [tagInput, addTag, tags, removeTag, getTagSuggestion]);

  const canProceedToNext = useMemo(() => {
    switch (currentStep) {
      case FlowStep.GAME_SEARCH:
        return !!selectedGame;
      case FlowStep.REVIEW_DETAILS:
        return !!(reviewTitle && language && playedOn);
      case FlowStep.FINAL_SUMMARY:
        return true;
      default:
        return false;
    }
  }, [currentStep, selectedGame, reviewTitle, language, playedOn]);

  // Helper function to check if platform should trigger performance survey
  const shouldShowPerformanceSurvey = useCallback((platform: string) => {
    const pcPlatforms = ['PC', 'PC (Microsoft Windows)', 'Linux'];
    return pcPlatforms.some(pcPlatform =>
      platform.toLowerCase().includes(pcPlatform.toLowerCase())
    );
  }, []);

  // Helper function to determine if we should show performance survey in final summary
  const shouldShowPerformanceSurveyInSummary = useCallback(() => {
    return (
      user && // User must be authenticated
      selectedGame?.name && // Game must be selected
      playedOn && // Platform must be selected
      shouldShowPerformanceSurvey(playedOn) && // Platform must support performance survey
      performanceSurveySkipped && // User must have skipped it initially
      !hasExistingSurvey // User must not have already submitted a survey for this game
    );
  }, [user, selectedGame?.name, playedOn, shouldShowPerformanceSurvey, performanceSurveySkipped, hasExistingSurvey]);

  // Handle performance survey close (both skip and completion)
  const handlePerformanceSurveyClose = useCallback((wasSkipped: boolean = true) => {
    setShowPerformanceSurvey(false);

    // Track if user skipped the survey (only if it was actually skipped, not completed)
    if (wasSkipped) {
      setPerformanceSurveySkipped(true);
    }

    // Continue to next step after survey is closed
    if (currentStep < FlowStep.FINAL_SUMMARY) {
      setCurrentStep(currentStep + 1);
    }
    // No external step progression needed
  }, [currentStep]);

  // Handle performance survey submission
  const handlePerformanceSurveySubmit = useCallback(async (data: PerformanceSurveyData) => {
    setPerformanceSurveyData(data);

    // Save to Supabase if user is authenticated
    if (user) {
      try {
        const result = await savePerformanceSurvey({
          surveyData: data,
          userId: user.uid,
          userEmail: user.email || undefined,
          gameTitle: selectedGame?.name,
          platform: playedOn
        });

        if (result.success) {
          console.log('Performance survey saved successfully');
          // Mark that user now has a survey for this game
          setHasExistingSurvey(true);
        } else {
          console.error('Failed to save performance survey:', result.error);
        }
      } catch (error) {
        console.error('Error saving performance survey:', error);
      }
    }

    // Close the survey and mark as completed (not skipped)
    handlePerformanceSurveyClose(false);
  }, [user, selectedGame, playedOn, handlePerformanceSurveyClose]);

  // Track user interactions with steps
  const markStepInteraction = useCallback((step: FlowStep) => {
    setStepInteractions(prev => {
      const newInteractions = [...prev];
      newInteractions[step] = true;
      return newInteractions;
    });
  }, []);

  // Update highest step reached
  const updateHighestStep = useCallback((step: FlowStep) => {
    setHighestStepReached(prev => Math.max(prev, step));
  }, []);

  // Initialize step tracking on component mount
  useEffect(() => {
    markStepInteraction(FlowStep.GAME_SEARCH);
  }, [markStepInteraction]);

  // Initialize tags from currentTags prop (for edit mode)
  useEffect(() => {
    if (currentTags && currentTags.length > 0) {
      console.log('[TitleYourQuest] Initializing tags from currentTags:', currentTags);
      setTags(currentTags);
    }
  }, [currentTags]);

  // Initialize game data from existing review data when in edit mode
  useEffect(() => {
    try {
      console.log('[TitleYourQuest] Edit mode useEffect triggered:', {
        isEditMode,
        gameName,
        selectedGame: !!selectedGame,
        igdbId,
        igdbSummary: !!igdbSummary,
        igdbAggregatedRating,
        igdbAggregatedRatingCount
      });

      if (isEditMode && gameName && !selectedGame) {
        console.log('[TitleYourQuest] Initializing game data for edit mode:', { gameName, igdbId });

        // Create a minimal game object from existing data to display in summary
        const mockGame: IGDBGame = {
          id: igdbId?.toString() || '0',
          name: gameName,
          ...(igdbSummary && { summary: igdbSummary }),
          ...(igdbAggregatedRating && { aggregated_rating: igdbAggregatedRating }),
          ...(igdbAggregatedRatingCount && { aggregated_rating_count: igdbAggregatedRatingCount })
        };

        console.log('[TitleYourQuest] Setting mock game for edit mode:', mockGame);
        setSelectedGame(mockGame);
      }
    } catch (error) {
      console.error('[TitleYourQuest] Error initializing game data for edit mode:', error);
    }
  }, [isEditMode, gameName, igdbId, igdbSummary, igdbAggregatedRating, igdbAggregatedRatingCount]);

  // Track interactions with review details step
  useEffect(() => {
    if (currentStep === FlowStep.REVIEW_DETAILS && (reviewTitle || language || playedOn)) {
      markStepInteraction(FlowStep.REVIEW_DETAILS);
    }
  }, [currentStep, reviewTitle, language, playedOn, markStepInteraction]);



  // Track interactions with final summary step
  useEffect(() => {
    if (currentStep === FlowStep.FINAL_SUMMARY) {
      markStepInteraction(FlowStep.FINAL_SUMMARY);
    }
  }, [currentStep, markStepInteraction]);

  const nextStep = useCallback(() => {
    if (canProceedToNext) {
      // Mark current step as interacted with
      markStepInteraction(currentStep);

      // Check if we should show performance survey when leaving review details step
      if (currentStep === FlowStep.REVIEW_DETAILS && shouldShowPerformanceSurvey(playedOn)) {
        setShowPerformanceSurvey(true);
        return;
      }

      if (currentStep < FlowStep.FINAL_SUMMARY) {
        const nextStepValue = currentStep + 1;
        setCurrentStep(nextStepValue);
        updateHighestStep(nextStepValue);
        markStepInteraction(nextStepValue);
      } else {
        console.log('[TitleYourQuest] Flow completed - no external progression needed');
        // No external step progression needed
      }
    }
  }, [currentStep, canProceedToNext, shouldShowPerformanceSurvey, playedOn, markStepInteraction, updateHighestStep]);

  const goToStep = useCallback((step: FlowStep) => {
    // Only allow navigation to visited steps or the next available step
    if (step <= highestStepReached || step <= currentStep + 1) {
      setCurrentStep(step);
      updateHighestStep(step);
      markStepInteraction(step);
    }
  }, [highestStepReached, currentStep, updateHighestStep, markStepInteraction]);

  // Inline editing handlers
  const startEditing = useCallback((field: string) => {
    // Special case: if editing game, go back to first step and clear game selection
    if (field === 'game') {
      setSelectedGame(null);
      setGameName('');
      setExpandSummary(false);
      setPlayedOn('');
      setSelectedGameId(null);
      setGameToLoad(null);

      // Clear IGDB metadata (but not mainImageUrl - user controls banner independently)
      setIgdbCoverUrl(''); // Clear IGDB cover URL
      setIgdbId(undefined);
      setIgdbSummary('');
      setIgdbDevelopers([]);
      setIgdbPublishers([]);
      setIgdbGameEngines([]);
      setIgdbPlayerPerspectives([]);
      setIgdbTimeToBeatNormally(undefined);
      setIgdbTimeToBeatCompletely(undefined);
      setIgdbAggregatedRating(undefined);
      setIgdbAggregatedRatingCount(undefined);
      setReleaseDate(undefined);

      // Navigate back to game search step
      goToStep(FlowStep.GAME_SEARCH);
      return;
    }

    setEditingField(field);
    // Mark final summary as interacted when user starts editing
    if (currentStep === FlowStep.FINAL_SUMMARY) {
      markStepInteraction(FlowStep.FINAL_SUMMARY);
    }
  }, [currentStep, markStepInteraction, goToStep]);

  const stopEditing = useCallback((e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    setEditingField(null);
  }, []);



  const renderGameSearch = () => {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
        className="bg-slate-800/50 border border-slate-700/50 rounded-lg"
      >
        <div className="p-4">
          {/* Conditional header based on whether game is selected */}
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-md bg-slate-800/60">
              <Gamepad2 className="h-4 w-4 text-slate-400" />
            </div>
            <div>
              <div className="font-mono text-sm">
                <span className="text-violet-400">//</span>
                <span className="text-slate-300 ml-1">
                  {selectedGame ? 'Selected Game' : 'Search Game'}
                </span>
              </div>
              <p className="text-slate-500 text-xs mt-1">
                {selectedGame
                  ? 'Review the selected game details below'
                  : 'Find and select the game you want to review'
                }
              </p>
            </div>
          </div>

          <div className="space-y-4">
            {/* Show search interface only when no game is selected */}
            {!selectedGame && (
              <>
                <GameSearchCombobox
                  onGameSelect={handleGameSelect}
                  initialSearchQuery={gameName}
                />
                {loading && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-sm text-blue-400 mt-2"
                  >
                    Loading game data...
                  </motion.div>
                )}
              </>
            )}

            {/* Show simplified game preview when selected */}
            <AnimatePresence>
              {selectedGame && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  transition={{ duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }}
                  className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-4 mt-4"
                >
                  {/* Game header with title and meta */}
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-slate-200 font-mono">
                        {selectedGame.name}
                      </h3>
                      <div className="flex items-center gap-3 mt-1 text-sm text-slate-400">
                        <span>{formatDate(selectedGame.releaseDate || selectedGame.first_release_date)}</span>
                        {getDevelopers(selectedGame).length > 0 && (
                          <span>{getDevelopers(selectedGame)[0]}</span>
                        )}
                      </div>
                    </div>
                    {selectedGame.cover?.url && (
                      <img
                        src={selectedGame.cover.url.replace('t_thumb', 't_cover_big')}
                        alt={selectedGame.name}
                        className="w-12 h-16 object-cover rounded border border-slate-600"
                      />
                    )}
                  </div>

                  {/* Game summary */}
                  {selectedGame.summary && (
                    <div className="mb-4">
                      <h4 className="text-xs font-semibold text-slate-400 uppercase tracking-wide mb-2 font-mono">
                        Summary
                      </h4>
                      <p className={`text-sm text-slate-300 leading-relaxed ${!expandSummary && selectedGame.summary.length > 150 ? 'line-clamp-3' : ''}`}>
                        {selectedGame.summary}
                      </p>
                      {selectedGame.summary.length > 150 && (
                        <button
                          type="button"
                          onClick={() => setExpandSummary(!expandSummary)}
                          className="text-xs text-blue-400 mt-1 hover:text-blue-300 transition-colors"
                          aria-label={expandSummary ? 'Show less summary' : 'Show more summary'}
                        >
                          {expandSummary ? 'less' : 'more'}
                        </button>
                      )}
                    </div>
                  )}

                  {/* IGDB Metadata Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    {/* Developers */}
                    {getDevelopers(selectedGame).length > 0 && (
                      <div>
                        <h4 className="text-xs font-semibold text-slate-400 uppercase tracking-wide mb-2 font-mono">
                          Developer{getDevelopers(selectedGame).length > 1 ? 's' : ''}
                        </h4>
                        <div className="flex flex-wrap gap-1">
                          {getDevelopers(selectedGame).slice(0, 3).map((dev, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 text-xs bg-slate-700/40 border border-slate-600/40 rounded text-slate-200 font-mono"
                            >
                              {dev}
                            </span>
                          ))}
                          {getDevelopers(selectedGame).length > 3 && (
                            <span className="px-2 py-1 text-xs bg-slate-700/40 border border-slate-600/40 rounded text-slate-400 font-mono">
                              +{getDevelopers(selectedGame).length - 3}
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Publishers */}
                    {getPublishers(selectedGame).length > 0 && (
                      <div>
                        <h4 className="text-xs font-semibold text-slate-400 uppercase tracking-wide mb-2 font-mono">
                          Publisher{getPublishers(selectedGame).length > 1 ? 's' : ''}
                        </h4>
                        <div className="flex flex-wrap gap-1">
                          {getPublishers(selectedGame).slice(0, 3).map((pub, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 text-xs bg-slate-700/40 border border-slate-600/40 rounded text-slate-200 font-mono"
                            >
                              {pub}
                            </span>
                          ))}
                          {getPublishers(selectedGame).length > 3 && (
                            <span className="px-2 py-1 text-xs bg-slate-700/40 border border-slate-600/40 rounded text-slate-400 font-mono">
                              +{getPublishers(selectedGame).length - 3}
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Genres */}
                    {selectedGame.genres && selectedGame.genres.length > 0 && (
                      <div>
                        <h4 className="text-xs font-semibold text-slate-400 uppercase tracking-wide mb-2 font-mono">
                          Genre{selectedGame.genres.length > 1 ? 's' : ''}
                        </h4>
                        <div className="flex flex-wrap gap-1">
                          {selectedGame.genres.slice(0, 4).map((genre, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 text-xs bg-slate-700/40 border border-slate-600/40 rounded text-slate-200 font-mono"
                            >
                              {typeof genre === 'string' ? genre : genre?.name || 'Unknown'}
                            </span>
                          ))}
                          {selectedGame.genres.length > 4 && (
                            <span className="px-2 py-1 text-xs bg-slate-700/40 border border-slate-600/40 rounded text-slate-400 font-mono">
                              +{selectedGame.genres.length - 4}
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Rating */}
                    {selectedGame.aggregated_rating && (
                      <div>
                        <h4 className="text-xs font-semibold text-slate-400 uppercase tracking-wide mb-2 font-mono">
                          IGDB Rating
                        </h4>
                        <div className="flex items-center gap-2">
                          <span className="px-2 py-1 text-xs bg-green-700/40 border border-green-600/40 rounded text-green-200 font-mono font-bold">
                            {Math.round(selectedGame.aggregated_rating)}/100
                          </span>
                          {selectedGame.aggregated_rating_count && (
                            <span className="text-xs text-slate-400 font-mono">
                              ({selectedGame.aggregated_rating_count} reviews)
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Game Engines */}
                    {selectedGame.game_engines && selectedGame.game_engines.length > 0 && (
                      <div>
                        <h4 className="text-xs font-semibold text-slate-400 uppercase tracking-wide mb-2 font-mono">
                          Game Engine{selectedGame.game_engines.length > 1 ? 's' : ''}
                        </h4>
                        <div className="flex flex-wrap gap-1">
                          {selectedGame.game_engines.slice(0, 2).map((engine, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 text-xs bg-slate-700/40 border border-slate-600/40 rounded text-slate-200 font-mono"
                            >
                              {typeof engine === 'string' ? engine : engine?.name || 'Unknown'}
                            </span>
                          ))}
                          {selectedGame.game_engines.length > 2 && (
                            <span className="px-2 py-1 text-xs bg-slate-700/40 border border-slate-600/40 rounded text-slate-400 font-mono">
                              +{selectedGame.game_engines.length - 2}
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Player Perspectives */}
                    {selectedGame.player_perspectives && selectedGame.player_perspectives.length > 0 && (
                      <div>
                        <h4 className="text-xs font-semibold text-slate-400 uppercase tracking-wide mb-2 font-mono">
                          Perspective{selectedGame.player_perspectives.length > 1 ? 's' : ''}
                        </h4>
                        <div className="flex flex-wrap gap-1">
                          {selectedGame.player_perspectives.slice(0, 3).map((perspective, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 text-xs bg-slate-700/40 border border-slate-600/40 rounded text-slate-200 font-mono"
                            >
                              {typeof perspective === 'string' ? perspective : perspective?.name || 'Unknown'}
                            </span>
                          ))}
                          {selectedGame.player_perspectives.length > 3 && (
                            <span className="px-2 py-1 text-xs bg-slate-700/40 border border-slate-600/40 rounded text-slate-400 font-mono">
                              +{selectedGame.player_perspectives.length - 3}
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Time to Beat */}
                    {selectedGame.time_to_beat && (selectedGame.time_to_beat.normally || selectedGame.time_to_beat.completely) && (
                      <div>
                        <h4 className="text-xs font-semibold text-slate-400 uppercase tracking-wide mb-2 font-mono">
                          Time to Beat
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {selectedGame.time_to_beat.normally && (
                            <span className="px-2 py-1 text-xs bg-blue-700/40 border border-blue-600/40 rounded text-blue-200 font-mono">
                              Normal: {Math.round(selectedGame.time_to_beat.normally / 3600)}h
                            </span>
                          )}
                          {selectedGame.time_to_beat.completely && (
                            <span className="px-2 py-1 text-xs bg-purple-700/40 border border-purple-600/40 rounded text-purple-200 font-mono">
                              Complete: {Math.round(selectedGame.time_to_beat.completely / 3600)}h
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Platforms */}
                  {selectedGame.platforms && selectedGame.platforms.length > 0 && (
                    <div>
                      <h4 className="text-xs font-semibold text-slate-400 uppercase tracking-wide mb-2 font-mono">
                        Available Platforms
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedGame.platforms.slice(0, 6).map((platform, index) => {
                          const platformName = typeof platform === 'string' ? platform : (platform?.name || platform?.toString() || 'Unknown');
                          return (
                            <button
                              key={`${platformName}-${index}`}
                              type="button"
                              onClick={() => setPlayedOn(platformName)}
                              className={`px-3 py-1 text-xs rounded-md border transition-colors font-mono ${
                                platformName === playedOn
                                  ? 'bg-slate-700 border-slate-500 text-slate-200'
                                  : 'bg-slate-800/50 border-slate-600 text-slate-400 hover:bg-slate-700/50'
                              }`}
                            >
                              {platformName}
                            </button>
                          );
                        })}
                        {selectedGame.platforms.length > 6 && (
                          <span className="px-3 py-1 text-xs bg-slate-800/50 border border-slate-600 text-slate-400 rounded-md font-mono">
                            +{selectedGame.platforms.length - 6} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>

            {/* Action buttons - Always visible */}

          </div>
        </div>
      </motion.div>
    );
  };

  const renderReviewDetails = () => {
    return (
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
        className="bg-slate-800/50 border border-slate-700/50 rounded-lg"
      >
        <div className="p-4">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-md bg-slate-800/60">
              <Edit3 className="h-4 w-4 text-slate-400" />
            </div>
            <div>
              <div className="font-mono text-sm">
                <span className="text-violet-400">//</span>
                <span className="text-slate-300 ml-1">Review Details</span>
              </div>
              <p className="text-slate-500 text-xs mt-1">
                Add essential information about your review
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="tyq-subsection">
              <div className="tyq-subsection-title">Basic Information</div>
              <div className="tyq-form-grid">
                <motion.div 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 }}
                  className="tyq-field-group"
                >
                  <label className="tyq-label tyq-label-required">title</label>
                  <input
                    placeholder="Your review title"
                    value={reviewTitle}
                    onChange={(e) => setReviewTitle(e.target.value)}
                    className="tyq-input"
                  />
                </motion.div>

                <motion.div 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  className="tyq-field-group"
                >
                  <label className="tyq-label tyq-label-required">language</label>
                  <Select value={language} onValueChange={setLanguage}>
                    <SelectTrigger className="tyq-select">
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      {languages.map(lang => (
                        <SelectItem key={lang.code} value={lang.code}>
                          {lang.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </motion.div>

                <motion.div 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                  className="tyq-field-group"
                >
                  <label className="tyq-label tyq-label-required">Where did you play it?</label>
                  <Select value={playedOn} onValueChange={setPlayedOn}>
                    <SelectTrigger className="tyq-select">
                      <SelectValue placeholder="Select platform" />
                    </SelectTrigger>
                    <SelectContent>
                      {playedOnPlatforms.map(platform => (
                        <SelectItem key={platform} value={platform}>
                          {platform}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </motion.div>

                <motion.div 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                  className="tyq-field-group"
                >
                  <label className="tyq-label">When did you play it? Roughly...</label>
                  <input
                    placeholder="MM/YYYY"
                    value={datePlayed}
                    onChange={(e) => handleDateInput(e.target.value)}
                    className="tyq-input"
                  />
                </motion.div>
              </div>
            </div>

            {/* Tags Section - Integrated */}
            <div className="tyq-subsection">
              <div className="tyq-subsection-title">Content Tags</div>
              <div className="space-y-3">
                {/* Current Tags Display */}
                <div className="flex flex-wrap gap-2 min-h-[2rem] p-3 bg-slate-900/30 border border-slate-700/50 rounded-md">
                  {tags.length > 0 ? (
                    tags.map((tag, index) => (
                      <div key={index} className="flex items-center gap-1 px-2 py-1 bg-slate-700/50 border border-slate-600/50 rounded text-xs text-slate-300">
                        <span className="font-mono">{tag}</span>
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="w-3 h-3 flex items-center justify-center rounded-full bg-slate-600/50 hover:bg-red-500/50 transition-colors"
                          aria-label={`Remove ${tag} tag`}
                        >
                          <X size={8} />
                        </button>
                      </div>
                    ))
                  ) : (
                    <span className="text-slate-500 text-sm">No tags added yet</span>
                  )}
                </div>

                {/* Tag Input */}
                <div className="flex gap-2">
                  <input
                    ref={tagInputRef}
                    placeholder="Add tag (max 18 characters)"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyDown={handleTagKeyDown}
                    className="tyq-input flex-1"
                    disabled={tags.length >= 8}
                  />
                  <button
                    type="button"
                    onClick={() => addTag(tagInput)}
                    disabled={!tagInput || tags.length >= 8 || !!getTagValidationError(tagInput)}
                    className="px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded text-slate-300 hover:bg-slate-600/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    aria-label="Add tag"
                  >
                    <Plus size={14} />
                  </button>
                </div>

                {/* Tag Counter and Validation */}
                <div className="flex items-center justify-between text-xs">
                  <span className="text-slate-500">
                    {tags.length}/8 tags
                  </span>
                  {getTagValidationError(tagInput) && (
                    <span className="text-red-400">
                      {getTagValidationError(tagInput)}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Platforms & Genres Section */}
            {selectedGame && (selectedGame.platforms?.length > 0 || selectedGame.genres?.length > 0) && (
              <div className="tyq-subsection">
                <div className="tyq-subsection-title">Game Metadata</div>
                <div className="space-y-4">
                  {/* Platforms */}
                  {selectedGame.platforms && selectedGame.platforms.length > 0 && (
                    <div>
                      <h4 className="text-xs font-semibold text-slate-400 uppercase tracking-wide mb-2 font-mono">
                        Available Platforms ({selectedGame.platforms.length})
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedGame.platforms.map((platform, index) => {
                          const platformName = typeof platform === 'string' ? platform : (platform?.name || platform?.toString() || 'Unknown');
                          const isSelected = Array.isArray(selectedPlatforms) && selectedPlatforms.includes(platformName);
                          return (
                            <button
                              key={`platform-${platformName}-${index}`}
                              type="button"
                              onClick={() => onPlatformToggle(platformName)}
                              className={`px-3 py-1 text-xs rounded-md border transition-colors font-mono ${
                                isSelected
                                  ? 'bg-blue-700/40 border-blue-600/40 text-blue-200'
                                  : 'bg-slate-800/50 border-slate-600 text-slate-400 hover:bg-slate-700/50'
                              }`}
                            >
                              {platformName}
                            </button>
                          );
                        })}
                      </div>
                      <p className="text-xs text-slate-500 mt-2">
                        Click to tag platforms this game is available on
                      </p>
                    </div>
                  )}

                  {/* Genres */}
                  {selectedGame.genres && selectedGame.genres.length > 0 && (
                    <div>
                      <h4 className="text-xs font-semibold text-slate-400 uppercase tracking-wide mb-2 font-mono">
                        Game Genres ({selectedGame.genres.length})
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedGame.genres.map((genre, index) => {
                          const genreName = typeof genre === 'string' ? genre : (genre?.name || genre?.toString() || 'Unknown');
                          const isSelected = Array.isArray(selectedGenres) && selectedGenres.includes(genreName);
                          return (
                            <button
                              key={`genre-${genreName}-${index}`}
                              type="button"
                              onClick={() => onGenreToggle(genreName)}
                              className={`px-3 py-1 text-xs rounded-md border transition-colors font-mono ${
                                isSelected
                                  ? 'bg-purple-700/40 border-purple-600/40 text-purple-200'
                                  : 'bg-slate-800/50 border-slate-600 text-slate-400 hover:bg-slate-700/50'
                              }`}
                            >
                              {genreName}
                            </button>
                          );
                        })}
                      </div>
                      <p className="text-xs text-slate-500 mt-2">
                        Click to tag genres that apply to this game
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

          </div>
        </div>
      </motion.div>
    );
  };

  const renderFinalSummary = () => {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
        className="bg-slate-800/50 border border-slate-700/50 rounded-lg"
      >
        <div className="p-3">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 rounded-md bg-slate-800/60">
              <FileText className="h-4 w-4 text-slate-400" />
            </div>
            <div>
              <div className="font-mono text-sm">
                <span className="text-violet-400">//</span>
                <span className="text-slate-300 ml-1">Review Summary</span>
              </div>
              <p className="text-slate-500 text-xs mt-1">
                Review your details - click sections to edit
              </p>
            </div>
          </div>

          <div className="space-y-1.5 w-full">
            {/* Game Section - Compact without image */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className={`tyq-summary-section ${isEditMode ? 'cursor-not-allowed opacity-60' : ''}`}
              onClick={() => !isEditMode && startEditing('game')}
            >
              <div className="tyq-summary-section-title">game</div>
              <div className="tyq-summary-section-content">
                {selectedGame ? (
                  <div>
                    <div className="font-medium">{selectedGame.name}</div>
                    <div className="text-xs tyq-text-muted mt-0.5">
                      {formatDate(selectedGame.releaseDate || selectedGame.first_release_date)}
                    </div>
                  </div>
                ) : (
                  <span className="tyq-text-muted">No game selected</span>
                )}
              </div>
            </motion.div>

            {/* Review Title - Compact */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className={`tyq-summary-section ${isEditMode ? 'cursor-not-allowed opacity-60' : ''}`}
              onClick={() => !isEditMode && startEditing('title')}
            >
              <div className="tyq-summary-section-title">title</div>
              <div className="tyq-summary-section-content">
                {editingField === 'title' && !isEditMode ? (
                  <div className="flex gap-2 max-w-md">
                    <input
                      value={reviewTitle}
                      onChange={(e) => setReviewTitle(e.target.value)}
                      className="tyq-inline-edit flex-1"
                      autoFocus
                    />
                    <button type="button" onClick={(e) => stopEditing(e)} className="tyq-button tyq-button-secondary" aria-label="Save title">
                      <Save size={12} />
                    </button>
                  </div>
                ) : (
                  <span className={!reviewTitle ? 'tyq-text-muted' : ''}>
                    {reviewTitle || 'No title set'}
                  </span>
                )}
              </div>
            </motion.div>

            {/* Details Grid - Compact */}
            <div className="grid grid-cols-2 gap-1.5 w-full">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
                className={`tyq-summary-section ${isEditMode ? 'cursor-not-allowed opacity-60' : ''}`}
                onClick={() => !isEditMode && startEditing('language')}
              >
                <div className="tyq-summary-section-title">language</div>
                <div className="tyq-summary-section-content">
                  {editingField === 'language' && !isEditMode ? (
                    <div className="flex gap-2">
                      <Select value={language} onValueChange={setLanguage}>
                        <SelectTrigger className="tyq-inline-edit">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {languages.map(lang => (
                            <SelectItem key={lang.code} value={lang.code}>
                              {lang.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <button type="button" onClick={(e) => stopEditing(e)} className="tyq-button tyq-button-secondary" aria-label="Save language">
                        <Save size={12} />
                      </button>
                    </div>
                  ) : (
                    <span className={!language ? 'tyq-text-muted' : ''}>
                      {languages.find(l => l.code === language)?.name || 'No language set'}
                    </span>
                  )}
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
                className={`tyq-summary-section ${isEditMode ? 'cursor-not-allowed opacity-60' : ''}`}
                onClick={() => !isEditMode && startEditing('platform')}
              >
                <div className="tyq-summary-section-title">platform</div>
                <div className="tyq-summary-section-content">
                  {editingField === 'platform' && !isEditMode ? (
                    <div className="flex gap-2">
                      <Select value={playedOn} onValueChange={setPlayedOn}>
                        <SelectTrigger className="tyq-inline-edit">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {playedOnPlatforms.map(platform => (
                            <SelectItem key={platform} value={platform}>
                              {platform}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <button type="button" onClick={(e) => stopEditing(e)} className="tyq-button tyq-button-secondary" aria-label="Save platform">
                        <Save size={12} />
                      </button>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      <span className={!playedOn ? 'tyq-text-muted' : ''}>
                        {playedOn || 'No platform set'}
                      </span>
                      {playedOn && shouldShowPerformanceSurveyButton(playedOn) && !isEditMode && (
                        <button
                          type="button"
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowPerformanceSurvey(true);
                          }}
                          className="tyq-button tyq-button-primary text-xs px-2 py-1"
                        >
                          Performance Survey
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </motion.div>
            </div>

            {/* Tags and Date Grid - Compact */}
            <div className="grid grid-cols-2 gap-1.5 w-full">
              {/* Tags */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
                className={`tyq-summary-section ${isEditMode ? 'cursor-not-allowed opacity-60' : ''}`}
                onClick={() => !isEditMode && startEditing('tags')}
              >
                <div className="tyq-summary-section-title">tags ({tags.length}/8)</div>
                <div className="tyq-summary-section-content">
                  {editingField === 'tags' && !isEditMode ? (
                    <div>
                      <div className="flex flex-wrap gap-1 mb-3">
                        {tags.map((tag, index) => (
                          <div key={index} className="tyq-tag-simple">
                            <span className="font-mono">{tag}</span>
                            <button
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                removeTag(tag);
                              }}
                              className="tyq-tag-remove-simple"
                              aria-label={`Remove ${tag} tag`}
                            >
                              <X size={8} />
                            </button>
                          </div>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <input
                          ref={tagInputRef}
                          placeholder="Add tag (max 18 characters)"
                          value={tagInput}
                          onChange={(e) => setTagInput(e.target.value)}
                          onKeyDown={handleTagKeyDown}
                          className="tyq-inline-edit flex-1"
                          disabled={tags.length >= 8}
                        />
                        <button
                          type="button"
                          onClick={() => addTag(tagInput)}
                          className="tyq-button tyq-button-secondary"
                          disabled={!tagInput || tags.length >= 8 || !!getTagValidationError(tagInput)}
                          aria-label="Add tag"
                        >
                          <Plus size={12} />
                        </button>
                        <button type="button" onClick={(e) => stopEditing(e)} className="tyq-button tyq-button-secondary" aria-label="Save tags">
                          <Save size={12} />
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-wrap gap-0.5">
                      {tags.length > 0 ? (
                        tags.map((tag, index) => (
                          <div key={index} className="tyq-tag-simple">
                            <span className="font-mono">{tag}</span>
                          </div>
                        ))
                      ) : (
                        <span className="tyq-text-muted">No tags set</span>
                      )}
                    </div>
                  )}
                </div>
              </motion.div>

              {/* Date Played */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
                className={`tyq-summary-section ${isEditMode ? 'cursor-not-allowed opacity-60' : ''}`}
                onClick={() => !isEditMode && startEditing('date')}
              >
                <div className="tyq-summary-section-title">date played</div>
                <div className="tyq-summary-section-content">
                  {editingField === 'date' && !isEditMode ? (
                    <div className="flex gap-2">
                      <input
                        value={datePlayed}
                        onChange={(e) => handleDateInput(e.target.value)}
                        className="tyq-inline-edit flex-1"
                        placeholder="MM/YYYY"
                      />
                      <button type="button" onClick={(e) => stopEditing(e)} className="tyq-button tyq-button-secondary" aria-label="Save date">
                        <Save size={12} />
                      </button>
                    </div>
                  ) : (
                    <span className={!datePlayed ? 'tyq-text-muted' : ''}>
                      {datePlayed || 'No date set'}
                    </span>
                  )}
                </div>
              </motion.div>
            </div>

            {/* Performance Survey Section - Only show if user skipped it and hasn't submitted one for this game */}
            {shouldShowPerformanceSurveyInSummary() && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
                className="tyq-summary-section bg-blue-900/20 border-blue-700/30"
              >
                <div className="tyq-summary-section-title text-blue-400">performance survey</div>
                <div className="tyq-summary-section-content">
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                      <span className="text-sm text-slate-300">
                        Help the community with your PC performance data
                      </span>
                      <span className="text-xs text-slate-500 mt-1">
                        for {selectedGame?.name} on {playedOn}
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={() => setShowPerformanceSurvey(true)}
                      className="tyq-button tyq-button-primary text-sm px-3 py-2"
                    >
                      Take Survey
                    </button>
                  </div>
                </div>
              </motion.div>
            )}


          </div>
        </div>
      </motion.div>
    );
  };



  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <AnimatePresence mode="wait">
          {/* In edit mode, show the normal form but with tags disabled */}
          {isEditMode ? (
            <motion.div key="edit-mode">
              {renderFinalSummary()}
            </motion.div>
          ) : (
            <>
              {currentStep === FlowStep.GAME_SEARCH && (
                <motion.div key="game-search">
                  {renderGameSearch()}
                </motion.div>
              )}
              {currentStep === FlowStep.REVIEW_DETAILS && (
                <motion.div key="review-details">
                  {renderReviewDetails()}
                </motion.div>
              )}
              {currentStep === FlowStep.FINAL_SUMMARY && (
                <motion.div key="final-summary">
                  {renderFinalSummary()}
                </motion.div>
              )}
            </>
          )}
        </AnimatePresence>
      </div>

      {/* Continue Button Footer - Game Search Step (Hidden in edit mode) */}
      <AnimatePresence>
        {!isEditMode && currentStep === FlowStep.GAME_SEARCH && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-4 gap-4"
          >
            <div className="flex items-center space-x-2 min-w-0">
              <div className={`w-2 h-2 rounded-full flex-shrink-0 ${
                selectedGame ? 'bg-green-400/60 animate-pulse' : 'bg-slate-500/60'
              }`} />
              <span className="text-sm text-slate-400/80 font-mono break-words">
                <span className="text-violet-400">//</span>
                <span className="ml-1">{selectedGame ? 'Game selected - ready to continue' : 'Select a game to continue'}</span>
              </span>
            </div>

            <div className="flex items-center space-x-3 flex-shrink-0">
              {/* Edit game selection button - only show when game is selected */}
              {selectedGame && (
                <button
                  type="button"
                  onClick={() => startEditing('game')}
                  className="review-continue-button review-continue-secondary"
                >
                  <div className="review-button-content">
                    <Edit3 className="review-button-arrow" />
                    <span className="review-code-brackets">&lt;</span>
                    Change Game
                    <span className="review-code-brackets">/&gt;</span>
                  </div>
                </button>
              )}

              {/* Continue button - always visible but disabled when no game selected */}
              <Button
                onClick={nextStep}
                disabled={!selectedGame}
                className={`review-continue-button ${
                  selectedGame ? 'review-continue-ready' : 'review-continue-disabled'
                }`}
              >
                <div className="review-button-content">
                  <span className="review-code-brackets">&lt;</span>
                  Continue
                  <span className="review-code-brackets">/&gt;</span>
                  <ArrowRight className="review-button-arrow" />
                </div>
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Continue Button Footer - Review Details Step (Hidden in edit mode) */}
      <AnimatePresence>
        {!isEditMode && currentStep === FlowStep.REVIEW_DETAILS && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-4 gap-4"
          >
            <div className="flex items-center space-x-2 min-w-0">
              <div className={`w-2 h-2 rounded-full flex-shrink-0 ${
                canProceedToNext ? 'bg-green-400/60 animate-pulse' : 'bg-slate-500/60'
              }`} />
              <span className="text-sm text-slate-400/80 font-mono break-words">
                <span className="text-violet-400">//</span>
                <span className="ml-1">{canProceedToNext ? 'Ready to continue to Summary' : 'Complete required fields to continue'}</span>
              </span>
            </div>

            <div className="flex items-center space-x-3 flex-shrink-0">
              <Button
                onClick={() => goToStep(currentStep - 1)}
                className="review-continue-button review-continue-secondary"
              >
                <div className="review-button-content">
                  <ArrowRight className="review-button-arrow" style={{ transform: 'rotate(180deg)' }} />
                  <span className="review-code-brackets">&lt;</span>
                  Previous
                  <span className="review-code-brackets">/&gt;</span>
                </div>
              </Button>

              <Button
                onClick={nextStep}
                disabled={!canProceedToNext}
                className={`review-continue-button ${
                  canProceedToNext ? 'review-continue-ready' : 'review-continue-disabled'
                }`}
              >
                <div className="review-button-content">
                  <span className="review-code-brackets">&lt;</span>
                  Continue
                  <span className="review-code-brackets">/&gt;</span>
                  <ArrowRight className="review-button-arrow" />
                </div>
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Continue Button Footer - Final Summary Step (Hidden in edit mode) */}
      <AnimatePresence>
        {!isEditMode && currentStep === FlowStep.FINAL_SUMMARY && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-4 gap-4"
          >
            <div className="flex items-center space-x-2 min-w-0">
              <div className="w-2 h-2 bg-green-400/60 rounded-full animate-pulse flex-shrink-0" />
              <span className="text-sm text-slate-400/80 font-mono break-words">
                <span className="text-violet-400">//</span>
                <span className="ml-1">Ready to continue to Media & Visuals</span>
              </span>
            </div>

            <div className="flex items-center space-x-3 flex-shrink-0">
              <Button
                onClick={() => goToStep(currentStep - 1)}
                className="review-continue-button review-continue-secondary"
              >
                <div className="review-button-content">
                  <ArrowRight className="review-button-arrow" style={{ transform: 'rotate(180deg)' }} />
                  <span className="review-code-brackets">&lt;</span>
                  Previous
                  <span className="review-code-brackets">/&gt;</span>
                </div>
              </Button>

              <Button
                onClick={() => {
                  // Mark step as completed and scroll to next section
                  console.log('[TitleYourQuest] Basic Information step completed');

                  // Find the next section after the current one
                  const currentSection = document.querySelector('[data-width]')?.querySelector('section');
                  if (currentSection) {
                    const allSections = document.querySelectorAll('[data-width] section');
                    const currentIndex = Array.from(allSections).indexOf(currentSection);
                    const nextSection = allSections[currentIndex + 1];

                    if (nextSection) {
                      const navbarHeight = 80;
                      const targetPosition = nextSection.getBoundingClientRect().top + window.pageYOffset - navbarHeight;

                      window.scrollTo({
                        top: Math.max(0, targetPosition),
                        behavior: 'smooth'
                      });
                    }
                  }
                }}
                className="review-continue-button review-continue-ready"
              >
                <div className="review-button-content">
                  <span className="review-code-brackets">&lt;</span>
                  Finish
                  <span className="review-code-brackets">/&gt;</span>
                  <CheckCircle2 className="review-button-arrow" />
                </div>
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Performance Survey Modal */}
      <PerformanceSurvey
        isOpen={showPerformanceSurvey}
        onClose={(wasSkipped) => handlePerformanceSurveyClose(wasSkipped)}
        onSubmit={handlePerformanceSurveySubmit}
        platform={playedOn}
        user={user}
        gameTitle={selectedGame?.name}
      />
    </div>
  );
};

export default TitleYourReview;