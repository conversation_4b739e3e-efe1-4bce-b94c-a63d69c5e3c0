// Game Cover Service - Handle IGDB cover caching to Supabase storage
import { createClient } from '@supabase/supabase-js';
import sharp from 'sharp';

// Supabase client with service role for storage operations
const supabaseServiceClient = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Cover size configurations
export const COVER_SIZES = {
  thumb: { width: 90, height: 128, suffix: 'thumb' },
  small: { width: 264, height: 374, suffix: 'small' },
  big: { width: 512, height: 725, suffix: 'big' }
} as const;

export type CoverSize = keyof typeof COVER_SIZES;

// Types
export interface CoverProcessingResult {
  success: boolean;
  supabaseCoverUrl?: string;
  error?: string;
  processingTimeMs?: number;
  fileSizeBytes?: number;
}

export interface CoverVariant {
  size: CoverSize;
  url: string;
  width: number;
  height: number;
  fileSizeBytes: number;
}

// Helper function to convert IGDB URL to high-res version
export function getHighResIgdbUrl(igdbUrl: string): string {
  if (!igdbUrl) return '';
  
  // Convert thumbnail URLs to cover_big for better quality
  return igdbUrl
    .replace('t_thumb', 't_cover_big')
    .replace('t_cover_small', 't_cover_big')
    .replace('t_micro', 't_cover_big');
}

// Generate storage path for cover
export function generateCoverPath(igdbId: string, size: CoverSize): string {
  return `covers/${igdbId}/${COVER_SIZES[size].suffix}.webp`;
}

// Download image from URL
async function downloadImage(url: string): Promise<Buffer> {
  const response = await fetch(url, {
    headers: {
      'User-Agent': 'CriticalPixel/1.0 (Game Review Platform)',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
  }

  const arrayBuffer = await response.arrayBuffer();
  return Buffer.from(arrayBuffer);
}

// Process image to specific size and format
async function processImageToSize(
  buffer: Buffer, 
  size: CoverSize
): Promise<{ buffer: Buffer; metadata: sharp.OutputInfo }> {
  const config = COVER_SIZES[size];
  
  const processedBuffer = await sharp(buffer)
    .resize(config.width, config.height, {
      fit: 'cover',
      position: 'center',
    })
    .webp({ 
      quality: 85, 
      effort: 4,
      smartSubsample: true 
    })
    .toBuffer({ resolveWithObject: true });

  return {
    buffer: processedBuffer.data,
    metadata: processedBuffer.info
  };
}

// Upload cover variant to Supabase storage
async function uploadCoverVariant(
  buffer: Buffer,
  path: string,
  metadata: { width: number; height: number; size: number }
): Promise<string> {
  const { data, error } = await supabaseServiceClient.storage
    .from('game-covers')
    .upload(path, buffer, {
      contentType: 'image/webp',
      cacheControl: '31536000', // 1 year cache
      upsert: true, // Allow overwriting existing files
      metadata: {
        width: metadata.width.toString(),
        height: metadata.height.toString(),
        originalSize: metadata.size.toString(),
      }
    });

  if (error) {
    throw new Error(`Failed to upload cover variant: ${error.message}`);
  }

  // Get public URL
  const { data: urlData } = supabaseServiceClient.storage
    .from('game-covers')
    .getPublicUrl(path);

  return urlData.publicUrl;
}

// Log cover processing event
async function logCoverProcessing(
  gameId: string,
  action: string,
  igdbUrl?: string,
  supabaseUrl?: string,
  errorMessage?: string,
  processingTimeMs?: number,
  fileSizeBytes?: number
): Promise<void> {
  try {
    await supabaseServiceClient.rpc('log_cover_processing', {
      p_game_id: gameId,
      p_action: action,
      p_igdb_url: igdbUrl,
      p_supabase_url: supabaseUrl,
      p_error_message: errorMessage,
      p_processing_time_ms: processingTimeMs,
      p_file_size_bytes: fileSizeBytes
    });
  } catch (error) {
    console.error('Failed to log cover processing event:', error);
  }
}

// Update game cover cache status in database
async function updateCoverCacheStatus(
  gameId: string,
  status: 'pending' | 'processing' | 'cached' | 'failed',
  cachedUrl?: string
): Promise<void> {
  const { error } = await supabaseServiceClient.rpc('update_cover_cache_status', {
    game_id: gameId,
    new_status: status,
    cached_url: cachedUrl
  });

  if (error) {
    throw new Error(`Failed to update cover cache status: ${error.message}`);
  }
}

// Main function to download and cache a game cover
export async function downloadAndCacheGameCover(
  gameId: string,
  igdbId: string,
  igdbCoverUrl: string
): Promise<CoverProcessingResult> {
  const startTime = Date.now();
  
  try {
    // Update status to processing
    await updateCoverCacheStatus(gameId, 'processing');
    await logCoverProcessing(gameId, 'cache_started', igdbCoverUrl);

    // Get high-resolution IGDB URL
    const highResUrl = getHighResIgdbUrl(igdbCoverUrl);
    
    // Download original image
    const originalBuffer = await downloadImage(highResUrl);
    
    // Process and upload all cover variants
    const variants: CoverVariant[] = [];
    let totalFileSize = 0;

    for (const [sizeKey, sizeConfig] of Object.entries(COVER_SIZES)) {
      const size = sizeKey as CoverSize;
      const processed = await processImageToSize(originalBuffer, size);
      const path = generateCoverPath(igdbId, size);
      
      const publicUrl = await uploadCoverVariant(processed.buffer, path, {
        width: sizeConfig.width,
        height: sizeConfig.height,
        size: processed.metadata.size
      });

      variants.push({
        size,
        url: publicUrl,
        width: sizeConfig.width,
        height: sizeConfig.height,
        fileSizeBytes: processed.metadata.size
      });

      totalFileSize += processed.metadata.size;
    }

    // Use the 'big' variant as the primary cached URL
    const primaryUrl = variants.find(v => v.size === 'big')?.url || variants[0].url;
    
    // Update database with cached URL
    await updateCoverCacheStatus(gameId, 'cached', primaryUrl);
    
    const processingTime = Date.now() - startTime;
    await logCoverProcessing(
      gameId, 
      'cache_completed', 
      igdbCoverUrl, 
      primaryUrl, 
      undefined, 
      processingTime, 
      totalFileSize
    );

    return {
      success: true,
      supabaseCoverUrl: primaryUrl,
      processingTimeMs: processingTime,
      fileSizeBytes: totalFileSize
    };

  } catch (error) {
    const processingTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    // Update status to failed
    await updateCoverCacheStatus(gameId, 'failed');
    await logCoverProcessing(
      gameId, 
      'cache_failed', 
      igdbCoverUrl, 
      undefined, 
      errorMessage, 
      processingTime
    );

    console.error(`Failed to cache cover for game ${gameId}:`, error);
    
    return {
      success: false,
      error: errorMessage,
      processingTimeMs: processingTime
    };
  }
}

// Get optimal cover URL for a game (cached or fallback)
export function getOptimalCoverUrl(game: {
  cover_cache_status?: string;
  supabase_cover_url?: string;
  cover_url?: string;
}, preferredSize: CoverSize = 'big'): string {
  // Return cached cover if available
  if (game.cover_cache_status === 'cached' && game.supabase_cover_url) {
    // If we want a different size, modify the URL
    if (preferredSize !== 'big') {
      return game.supabase_cover_url.replace('/big.webp', `/${COVER_SIZES[preferredSize].suffix}.webp`);
    }
    return game.supabase_cover_url;
  }
  
  // Fallback to IGDB URL
  return game.cover_url || '';
}

// Get cover URL for specific size
export function getCoverUrlForSize(
  game: {
    cover_cache_status?: string;
    supabase_cover_url?: string;
    cover_url?: string;
  },
  size: CoverSize,
  igdbId?: string
): string {
  // If we have cached covers, return the specific size
  if (game.cover_cache_status === 'cached' && game.supabase_cover_url && igdbId) {
    const { data } = supabaseServiceClient.storage
      .from('game-covers')
      .getPublicUrl(generateCoverPath(igdbId, size));
    return data.publicUrl;
  }
  
  // Fallback to IGDB URL with size conversion
  if (game.cover_url) {
    const sizeMap = {
      thumb: 't_thumb',
      small: 't_cover_small', 
      big: 't_cover_big'
    };
    return game.cover_url.replace(/t_\w+/, sizeMap[size]);
  }
  
  return '';
}

// Check if cover needs recaching (e.g., if it's old or failed)
export function shouldRecacheCover(game: {
  cover_cache_status?: string;
  igdb_cover_cached_at?: string;
}): boolean {
  if (!game.cover_cache_status || game.cover_cache_status === 'pending') {
    return true;
  }
  
  if (game.cover_cache_status === 'failed') {
    // Retry failed covers after 24 hours
    if (game.igdb_cover_cached_at) {
      const cachedAt = new Date(game.igdb_cover_cached_at);
      const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      return cachedAt < dayAgo;
    }
    return true;
  }
  
  return false;
}
