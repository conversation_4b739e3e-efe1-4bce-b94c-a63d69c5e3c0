import React, { useState } from 'react';
import { Monitor, Sparkles, Target, ChevronDown, ChevronUp } from 'lucide-react';
import PlatformIcon from './PlatformIcon';

interface LinearToggleProps {
  platforms: string[];
  genres: string[];
  tags?: string[];
}

const LinearToggle = ({ platforms = [], genres = [], tags = [] }: LinearToggleProps) => {
  const [displayMode, setDisplayMode] = useState<'platforms' | 'genres' | 'tags'>('tags');
  const [isExpanded, setIsExpanded] = useState(false);


  const categories = [
    { key: 'platforms' as const, label: 'Platforms', icon: Monitor, items: platforms },
    { key: 'genres' as const, label: 'Genres', icon: Sparkles, items: genres },
    { key: 'tags' as const, label: 'Tags', icon: Target, items: tags }
  ];

  const currentCategory = categories.find(cat => cat.key === displayMode);
  const currentItems = currentCategory?.items || [];

  return (
    <div className="w-full">
      {/* Header with category selector and expand toggle */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          {categories.map((category) => {
            const Icon = category.icon;
            const isActive = displayMode === category.key;
            const hasItems = category.items && category.items.length > 0;
            
            return (
              <button
                key={category.key}
                onClick={() => {
                  setDisplayMode(category.key);
                  if (!isExpanded && hasItems) setIsExpanded(true);
                }}
                disabled={!hasItems}
                className={`flex items-center gap-2 px-3 py-1.5 rounded-lg text-sm transition-all duration-200 ${
                  isActive && hasItems
                    ? 'bg-slate-700/60 text-slate-200 border border-slate-600/50' 
                    : hasItems
                      ? 'text-slate-400 hover:text-slate-300 hover:bg-slate-800/40'
                      : 'text-slate-600 cursor-not-allowed'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{category.label}</span>
                {hasItems && (
                  <span className="text-xs bg-slate-600/40 px-1.5 py-0.5 rounded">
                    {category.items.length}
                  </span>
                )}
              </button>
            );
          })}
        </div>
        
        {currentItems.length > 0 && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-1 px-2 py-1 text-slate-400 hover:text-slate-300 transition-colors text-sm"
          >
            <span>{isExpanded ? 'Hide' : 'Show'}</span>
            {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </button>
        )}
      </div>

      {/* Content area - removed container background and borders */}
      {isExpanded && currentItems.length > 0 && (
        <div className="flex flex-wrap gap-1.5">
          {currentItems.map((item, index) => (
            <div
              key={`${displayMode}-${item}`}
              className="flex items-center gap-1.5 px-2 py-1 bg-slate-700/40 border border-slate-600/40 rounded-md text-xs font-mono text-slate-200 hover:bg-slate-700/60 transition-colors"
            >
              {displayMode === 'platforms' ? (
                <PlatformIcon platform={item} />
              ) : displayMode === 'genres' ? (
                <Sparkles className="w-3 h-3 text-slate-400" />
              ) : (
                <Target className="w-3 h-3 text-slate-400" />
              )}
              <span>
                {typeof item === 'string' 
                  ? item 
                  : typeof item === 'object' && item?.name 
                    ? item.name 
                    : JSON.stringify(item).replace(/[{}'"]/g, '').replace('name:', '') || 'Unknown'
                }
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default LinearToggle;
