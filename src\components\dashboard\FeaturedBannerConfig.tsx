'use client';

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import Image from 'next/image';
import {
  Eye,
  Heart,
  MessageSquare,
  Loader2,
  Check,
  X,
  Trash2,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  Edit3,
  Save,
  Gamepad2,
  Zap,
  Target,
  Layers,
  ShoppingCart,
  Link2,
  Info,
  Sparkles
} from 'lucide-react';
import EnebaIcon from '@/components/ui/icons/stores/EnebaIcon';
import G2AIcon from '@/components/ui/icons/stores/G2AIcon';
import GOGIcon from '@/components/ui/icons/stores/GOGIcon';
import HRKIcon from '@/components/ui/icons/stores/HRKIcon';
import InstantGamingIcon from '@/components/ui/icons/stores/InstantGamingIcon';
import KinguinIcon from '@/components/ui/icons/stores/KinguinIcon';
import NuuvemIcon from '@/components/ui/icons/stores/NuuvemIcon';
import { getUserReviews } from '@/app/u/actions-content';
import type { ContentFilters } from '@/types/user-content';

interface UserReview {
  id: string;
  user_id: string;
  game_name: string;
  game_image?: string;
  igdb_cover_url?: string;
  rating: number;
  review_text: string;
  created_at: string;
  updated_at?: string;
  likes_count: number;
  views_count: number;
  comments_count: number;
  is_featured: boolean;
  is_public: boolean;
  platform?: string;
  playtime_hours?: number;
  tags?: string[];
  title: string;
  slug: string;
}

interface StoreLink {
  id?: string;
  store_name: string;
  price: string;
  original_price?: string;
  store_url: string;
  display_order: number;
  color_gradient?: string;
  is_active?: boolean;
  isEditing?: boolean;
}

interface FeaturedBannerConfigProps {
  userId: string;
  className?: string;
}

const FeaturedBannerConfig: React.FC<FeaturedBannerConfigProps> = ({
  userId,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [userReviews, setUserReviews] = useState<UserReview[]>([]);
  const [selectedReview, setSelectedReview] = useState<UserReview | null>(null);
  const [currentFeaturedReview, setCurrentFeaturedReview] = useState<UserReview | null>(null);
  const [sortBy, setSortBy] = useState<'created_at' | 'views_count' | 'likes_count' | 'updated_at' | 'rating'>('created_at');
  const [isExpanded, setIsExpanded] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Store links management
  const [storeLinks, setStoreLinks] = useState<StoreLink[]>([]);
  const [isLoadingStoreLinks, setIsLoadingStoreLinks] = useState(false);
  const [isSavingStoreLinks, setIsSavingStoreLinks] = useState(false);

  const { toast } = useToast();

  // Debounce search term to prevent excessive API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Load user's reviews on mount and when sort changes
  useEffect(() => {
    loadUserReviews();
    loadStoreLinks();
  }, [userId, sortBy]);

  // Reload when debounced search term changes
  useEffect(() => {
    loadUserReviews();
  }, [debouncedSearchTerm]);

  const loadUserReviews = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log('Loading user reviews for userId:', userId, 'with sort:', sortBy);

      // Create filters for sorting and limiting results
      const filters: ContentFilters = {
        sort_by: sortBy,
        sort_order: 'desc',
        limit: debouncedSearchTerm ? 20 : 2 // Show more when searching, default to 2
      };

      const response = await getUserReviews(userId, filters);
      console.log('getUserReviews response:', response);

      if (response && response.success && response.data) {
        console.log('Setting user reviews:', response.data);
        setUserReviews(response.data);

        // Find current featured review
        const featured = response.data.find(review => review.is_featured);
        console.log('Found featured review:', featured);
        if (featured) {
          setCurrentFeaturedReview(featured);
          setSelectedReview(featured);
        }
      } else {
        console.error('Failed to load reviews:', response?.error);
        toast({
          title: "Error",
          description: response?.error || "Failed to load reviews",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading user reviews:', error);
      toast({
        title: "Error",
        description: "Failed to load reviews",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [userId, sortBy, debouncedSearchTerm]);

  const handleSetFeatured = async () => {
    if (!selectedReview) return;

    try {
      setIsSaving(true);
      
      // Use FormData for server action
      const formData = new FormData();
      formData.append('action', 'setFeatured');
      formData.append('userId', userId);
      formData.append('reviewId', selectedReview.id);
      
      // Call server action via form submission
      const response = await fetch('/api/u/featured-review', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('🎯 Set featured result:', result);

      if (result && result.success) {
        toast({
          title: "Sucesso",
          description: `"${selectedReview.game_name}" agora é seu review em destaque!`,
        });
        
        setCurrentFeaturedReview(selectedReview);
        
        // Update the review list to reflect changes
        const updatedReviews = userReviews.map(review => ({
          ...review,
          is_featured: review.id === selectedReview.id
        }));
        setUserReviews(updatedReviews);
      } else {
        console.error('❌ Set featured failed:', result);
        toast({
          title: "Erro",
          description: (result && result.error) || "Falha ao definir review em destaque",
          variant: "destructive",
        });
      }
      
    } catch (error) {
      console.error('Error setting featured review:', error);
      toast({
        title: "Erro",
        description: "Falha ao definir review em destaque",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleRemoveFeatured = async () => {
    try {
      setIsSaving(true);
      
      // Use FormData for server action
      const formData = new FormData();
      formData.append('action', 'removeFeatured');
      formData.append('userId', userId);
      if (currentFeaturedReview?.id) {
        formData.append('reviewId', currentFeaturedReview.id);
      }
      
      // Call server action via API
      const response = await fetch('/api/u/featured-review', {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      console.log('🗑️ Remove featured result:', result);
      
      if (result && result.success) {
        toast({
          title: "Sucesso",
          description: "Review em destaque removido",
        });
        
        setCurrentFeaturedReview(null);
        setSelectedReview(null);
        
        // Update the review list to reflect changes
        setUserReviews(prev => prev.map(review => ({
          ...review,
          is_featured: false
        })));
              } else {
          console.error('❌ Remove featured failed:', result);
          toast({
            title: "Erro",
            description: (result && result.error) || "Falha ao remover review em destaque",
            variant: "destructive",
          });
        }
      
    } catch (error) {
      console.error('Error removing featured review:', error);
      toast({
        title: "Erro",
        description: "Falha ao remover review em destaque",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Store Links Management Functions
  const loadStoreLinks = async () => {
    try {
      setIsLoadingStoreLinks(true);

      const { createClient } = await import('@/lib/supabase/client');
      const supabase = createClient();

      const { data, error } = await supabase
        .from('featured_review_store_links')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error loading store links:', error);
        return;
      }

      setStoreLinks(data || []);
    } catch (error) {
      console.error('Error loading store links:', error);
    } finally {
      setIsLoadingStoreLinks(false);
    }
  };

  const addStoreLink = () => {
    if (storeLinks.length >= 3) {
      toast({
        title: "Limite atingido",
        description: "Você pode adicionar no máximo 3 links de lojas",
        variant: "destructive",
      });
      return;
    }

    const newLink: StoreLink = {
      store_name: '',
      price: '',
      original_price: '',
      store_url: '',
      display_order: storeLinks.length + 1,
      color_gradient: getRandomGradient(),
      is_active: true,
      isEditing: true // Auto-open edit mode for new links
    };

    setStoreLinks([...storeLinks, newLink]);
  };

  const removeStoreLink = async (index: number) => {
    const linkToRemove = storeLinks[index];

    if (linkToRemove.id) {
      try {
        const { createClient } = await import('@/lib/supabase/client');
        const supabase = createClient();

        await supabase
          .from('featured_review_store_links')
          .delete()
          .eq('id', linkToRemove.id);
      } catch (error) {
        console.error('Error deleting store link:', error);
      }
    }

    const updatedLinks = storeLinks.filter((_, i) => i !== index);
    // Reorder display_order
    const reorderedLinks = updatedLinks.map((link, i) => ({
      ...link,
      display_order: i + 1
    }));

    setStoreLinks(reorderedLinks);
  };

  const updateStoreLink = (index: number, field: keyof StoreLink, value: string) => {
    const updatedLinks = [...storeLinks];
    updatedLinks[index] = { ...updatedLinks[index], [field]: value };
    setStoreLinks(updatedLinks);
  };

  const saveStoreLinks = async () => {
    try {
      setIsSavingStoreLinks(true);

      const { createClient } = await import('@/lib/supabase/client');
      const supabase = createClient();

      // Validate all links have required fields
      const validLinks = storeLinks.filter(link =>
        link.store_name.trim() &&
        link.price.trim() &&
        link.store_url.trim()
      );

      if (validLinks.length === 0) {
        toast({
          title: "Erro",
          description: "Preencha pelo menos um link completo",
          variant: "destructive",
        });
        return;
      }

      // Delete existing links for this user
      await supabase
        .from('featured_review_store_links')
        .delete()
        .eq('user_id', userId);

      // Insert new links
      const linksToInsert = validLinks.map((link, index) => ({
        user_id: userId,
        store_name: link.store_name,
        price: link.price,
        original_price: link.original_price || null,
        store_url: link.store_url,
        display_order: index + 1,
        color_gradient: link.color_gradient,
        is_active: true
      }));

      const { error } = await supabase
        .from('featured_review_store_links')
        .insert(linksToInsert);

      if (error) {
        throw error;
      }

      toast({
        title: "Sucesso",
        description: "Links das lojas salvos com sucesso!",
      });

      loadStoreLinks(); // Reload to get IDs
    } catch (error) {
      console.error('Error saving store links:', error);
      toast({
        title: "Erro",
        description: "Falha ao salvar links das lojas",
        variant: "destructive",
      });
    } finally {
      setIsSavingStoreLinks(false);
    }
  };

  const getRandomGradient = () => {
    const gradients = [
      'from-blue-500/90 to-blue-600/95',
      'from-purple-500/90 to-purple-600/95',
      'from-emerald-500/90 to-emerald-600/95',
      'from-orange-500/90 to-orange-600/95',
      'from-red-500/90 to-red-600/95',
      'from-indigo-500/90 to-indigo-600/95'
    ];
    return gradients[Math.floor(Math.random() * gradients.length)];
  };

  // Review Item Component with IGDB cover prioritization
  const ReviewItem: React.FC<{ review: UserReview; isSelected: boolean; onClick: () => void }> = ({
    review,
    isSelected,
    onClick
  }) => {
    // SteamGridDB hook with client-side only execution (as fallback)
    const [steamGridIcon, setSteamGridIcon] = useState<string | null>(null);
    const [iconLoading, setIconLoading] = useState(false);

    // Determine the best image source with priority: IGDB > main_image > SteamGridDB
    const getImageSource = () => {
      if (review.igdb_cover_url) return review.igdb_cover_url;
      if (review.game_image) return review.game_image;
      return steamGridIcon;
    };

    useEffect(() => {
      // Only fetch SteamGridDB if we don't have IGDB cover or main image
      if (typeof window === 'undefined' || !review.game_name?.trim() || review.igdb_cover_url || review.game_image) {
        setIconLoading(false);
        return;
      }

      const fetchSteamGridIcon = async () => {
        try {
          setIconLoading(true);

          // Clean game name
          const cleanGameName = review.game_name
            .replace(/[^\w\s]/g, '')
            .replace(/\s+/g, ' ')
            .trim();

          // Search for the game
          const searchResponse = await fetch('/api/steamgriddb/search', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ query: cleanGameName }),
          });

          if (!searchResponse.ok) {
            setSteamGridIcon(null);
            return;
          }

          const searchResult = await searchResponse.json();
          const games = searchResult.data || [];

          if (!games.length) {
            setSteamGridIcon(null);
            return;
          }

          // Get icons for first game
          const iconsResponse = await fetch('/api/steamgriddb/icons', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              gameId: games[0].id,
              limit: 3
            }),
          });

          if (!iconsResponse.ok) {
            setSteamGridIcon(null);
            return;
          }

          const iconsResult = await iconsResponse.json();
          const icons = iconsResult.data || [];

          if (icons.length > 0) {
            const preferredIcon = icons.find(icon => icon.style === 'white_logo') || icons[0];
            setSteamGridIcon(preferredIcon.thumb || preferredIcon.url);
          } else {
            setSteamGridIcon(null);
          }
        } catch (error) {
          console.error('Error fetching SteamGrid icon:', error);
          setSteamGridIcon(null);
        } finally {
          setIconLoading(false);
        }
      };

      fetchSteamGridIcon();
    }, [review.game_name, review.igdb_cover_url, review.game_image]);

    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`group p-4 border rounded-xl cursor-pointer transition-all duration-300 ${
          isSelected
            ? 'border-purple-500/60 bg-gradient-to-r from-purple-500/10 via-purple-400/5 to-cyan-400/10 shadow-lg shadow-purple-500/20'
            : 'border-gray-700/50 bg-gradient-to-r from-gray-800/40 to-gray-700/30 hover:border-purple-400/40 hover:shadow-md hover:shadow-purple-500/10'
        }`}
        onClick={onClick}
      >
        <div className="flex items-center gap-4">
          {/* Game Icon with IGDB cover prioritization */}
          <div className="flex-shrink-0 w-12 h-12 bg-slate-800/60 border border-slate-700/50 rounded-lg overflow-hidden relative">
            {iconLoading ? (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-slate-800 via-slate-700 to-slate-800 relative">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-blue-900/20 animate-pulse"></div>
                <Gamepad2 className="text-slate-400 animate-pulse" size={20} />
              </div>
            ) : getImageSource() ? (
              <Image
                src={getImageSource() || ''}
                alt={review.game_name}
                width={48}
                height={48}
                className="w-full h-full object-cover"
                sizes="48px"
                onError={() => {
                  // If IGDB cover fails, try to fallback to SteamGridDB
                  if (review.igdb_cover_url && !steamGridIcon) {
                    setSteamGridIcon(null);
                  }
                }}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-slate-800 via-slate-700 to-slate-800 relative">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-blue-900/20"></div>
                <div className="relative z-10">
                  <Gamepad2 className="text-slate-400" size={20} />
                </div>
                <div className="absolute top-1 right-1 w-1 h-1 bg-purple-500/60 rounded-full"></div>
                <div className="absolute bottom-1 left-1 w-1 h-1 bg-blue-500/60 rounded-full"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs text-slate-500 font-mono opacity-50">
                  {review.game_name.slice(0, 2).toUpperCase()}
                </div>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-2">
              <h4 className="font-semibold text-white text-sm font-['Lato'] truncate group-hover:text-purple-200 transition-colors">
                {review.game_name}
              </h4>
              {review.is_featured && (
                <Badge variant="secondary" className="text-xs bg-purple-600/80 text-purple-100 font-['Lato'] border border-purple-500/50">
                  <Sparkles className="h-3 w-3 mr-1" />
                  Featured
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-4 text-xs text-gray-400 font-['Lato']">
              <span className="flex items-center gap-1 hover:text-purple-300 transition-colors">
                <Eye className="h-3 w-3" />
                {review.views_count?.toLocaleString() || '0'}
              </span>
              <span className="flex items-center gap-1 hover:text-red-300 transition-colors">
                <Heart className="h-3 w-3" />
                {review.likes_count?.toLocaleString() || '0'}
              </span>
              <span className="flex items-center gap-1 hover:text-blue-300 transition-colors">
                <MessageSquare className="h-3 w-3" />
                {review.comments_count?.toLocaleString() || '0'}
              </span>
              <span className="text-gray-500">•</span>
              <span className="text-gray-500">
                {review.rating}/5 ★
              </span>
            </div>
          </div>

          {/* Selection Indicator */}
          <div className={`flex-shrink-0 transition-all duration-200 ${
            isSelected ? 'scale-100 opacity-100' : 'scale-75 opacity-0 group-hover:scale-100 group-hover:opacity-50'
          }`}>
            <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
              isSelected 
                ? 'border-purple-400 bg-purple-500' 
                : 'border-gray-500 bg-transparent'
            }`}>
              {isSelected && <Check className="h-3 w-3 text-white" />}
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  // Memoize filtered reviews to prevent unnecessary re-calculations
  const filteredReviews = useMemo(() => {
    if (!debouncedSearchTerm) return userReviews;
    return userReviews.filter(review =>
      review.game_name.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
    );
  }, [userReviews, debouncedSearchTerm]);

  // Handle search input change without losing focus
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    
    // Maintain focus on the input after state update
    setTimeout(() => {
      if (searchInputRef.current && document.activeElement !== searchInputRef.current) {
        searchInputRef.current.focus();
        // Restore cursor position
        const cursorPosition = newValue.length;
        searchInputRef.current.setSelectionRange(cursorPosition, cursorPosition);
      }
    }, 0);
  }, []);

  // Store name suggestions - updated list as requested
  const storeNameSuggestions = [
    'Eneba', 
    'G2A', 
    'GOG', 
    'HRK', 
    'Instant Gaming', 
    'Kinguin', 
    'Nuuvem'
  ];

  // Store icon mapping
  const getStoreIcon = (storeName: string) => {
    const iconProps = { className: "h-4 w-4" };
    switch (storeName) {
      case 'Eneba': return <EnebaIcon {...iconProps} />;
      case 'G2A': return <G2AIcon {...iconProps} />;
      case 'GOG': return <GOGIcon {...iconProps} />;
      case 'HRK': return <HRKIcon {...iconProps} />;
      case 'Instant Gaming': return <InstantGamingIcon {...iconProps} />;
      case 'Kinguin': return <KinguinIcon {...iconProps} />;
      case 'Nuuvem': return <NuuvemIcon {...iconProps} />;
      default: return <ShoppingCart {...iconProps} />;
    }
  };

  const toggleStoreEditMode = (index: number) => {
    const updatedLinks = [...storeLinks];
    updatedLinks[index] = { 
      ...updatedLinks[index], 
      isEditing: !updatedLinks[index].isEditing 
    };
    setStoreLinks(updatedLinks);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Featured Banner Control Card */}
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardHeader 
          className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg text-white font-mono">
                <span className="text-purple-400 mr-1">//</span>
                Featured Review Banner
              </CardTitle>
              <p className="text-xs text-gray-400 mt-1 font-mono">
                Select one of your reviews to be featured prominently on your profile
              </p>
            </div>
            <div className="text-gray-400 hover:text-white ml-4">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </div>
        </CardHeader>
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ 
                duration: 0.3, 
                ease: "easeInOut",
                opacity: { duration: 0.2 }
              }}
              style={{ overflow: 'hidden' }}
            >
              <CardContent className="space-y-4">
                {/* Current Featured Review Display */}
                {currentFeaturedReview && (
                  <div className="p-3 bg-gray-800/50 border border-gray-700/50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-200 text-sm font-['Lato']">Currently Featured</h4>
                      <Button
                        onClick={handleRemoveFeatured}
                        disabled={isSaving}
                        variant="outline"
                        size="sm"
                        className="text-red-400 border-red-400/50 hover:bg-red-400/10 font-['Lato']"
                      >
                        {isSaving ? <Loader2 className="h-4 w-4 animate-spin" /> : <X className="h-4 w-4" />}
                        Remove
                      </Button>
                    </div>
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-white font-['Lato']">{currentFeaturedReview.game_name}</h3>
                      <div className="flex items-center gap-3 text-xs text-slate-400 font-['Lato']">
                        <span className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {currentFeaturedReview.views_count}
                        </span>
                        <span className="flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          {currentFeaturedReview.likes_count}
                        </span>
                        <span className="flex items-center gap-1">
                          <MessageSquare className="h-3 w-3" />
                          {currentFeaturedReview.comments_count}
                        </span>
                      </div>
                    </div>
                  </div>
                )}

          {/* Search and Filter Reviews */}
          <div className="space-y-3 p-4 bg-slate-800/60 border border-slate-700/60 rounded-lg">
            <Label htmlFor="review-search" className="text-sm text-gray-300 font-mono">Search Your Reviews</Label>
            <div className="flex gap-3">
              <div className="relative flex-1">
                <Target className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-purple-400" />
                <Input
                  ref={searchInputRef}
                  id="review-search"
                  placeholder="Search reviews by game name..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="pl-10 bg-slate-900/80 border-slate-600 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 text-white placeholder:text-gray-400 font-['Lato'] transition-all duration-200"
                  disabled={isLoading}
                />
              </div>
              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-48 bg-slate-900/80 border-slate-600 focus:border-purple-400 text-white font-['Lato']">
                  <Layers className="h-4 w-4 mr-2 text-purple-400" />
                  <SelectValue placeholder="Sort by..." />
                </SelectTrigger>
                <SelectContent className="bg-slate-900 border-slate-700">
                  <SelectItem value="created_at" className="font-['Lato']">Latest</SelectItem>
                  <SelectItem value="views_count" className="font-['Lato']">Most Views</SelectItem>
                  <SelectItem value="likes_count" className="font-['Lato']">Most Likes</SelectItem>
                  <SelectItem value="rating" className="font-['Lato']">Highest Rating</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Reviews List */}
          <div className="space-y-3 max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-purple-500 scrollbar-track-transparent">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-slate-400" />
                <span className="ml-2 text-slate-400 font-['Lato']">Loading reviews...</span>
              </div>
            ) : filteredReviews.length === 0 ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-slate-800/50 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Gamepad2 className="w-8 h-8 text-slate-600" />
                </div>
                <h3 className="text-lg font-medium text-slate-300 mb-2 font-mono">
                  {searchTerm ? 'No reviews found' : 'No reviews yet'}
                </h3>
                <p className="text-slate-500 font-['Lato'] text-sm">
                  {searchTerm 
                    ? 'Try adjusting your search terms'
                    : 'Create your first review to get started!'
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredReviews.map((review) => (
                  <ReviewItem
                    key={review.id}
                    review={review}
                    isSelected={selectedReview?.id === review.id}
                    onClick={() => setSelectedReview(review)}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          {selectedReview && selectedReview.id !== currentFeaturedReview?.id && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="pt-4 border-t border-slate-700/50"
            >
              <Button
                onClick={handleSetFeatured}
                disabled={isSaving}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white font-['Lato'] font-medium"
              >
                {isSaving ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Gamepad2 className="h-4 w-4 mr-2" />
                )}
                Set as Featured Review
              </Button>
            </motion.div>
          )}

          {/* Store Links Section */}
          <div className="pt-6 border-t border-gray-700/50">
            <div className="mb-6">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-lg flex items-center justify-center border border-purple-500/30">
                  <ShoppingCart className="h-4 w-4 text-purple-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white font-mono">
                    Store Links & Pricing
                  </h3>
                  <p className="text-xs text-slate-400 font-mono">
                    Add purchase links for your featured game (up to 3 stores)
                  </p>
                </div>
              </div>
              
              {/* Currency Info */}
              <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg p-3 mb-4">
                <div className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-blue-400 mt-0.5 flex-shrink-0" />
                  <div className="text-xs text-slate-300 leading-relaxed">
                    <strong className="text-blue-400">Currency Support:</strong> Use any currency symbol you want! 
                    Examples: $29.99, €24.99, £19.99, ¥2,990, R$89.90, ₹1,999, etc.
                  </div>
                </div>
              </div>
            </div>

            {/* Store Links List */}
            <div className="space-y-4">
              {storeLinks.map((link, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="group relative overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-slate-800/40 via-slate-700/20 to-slate-800/40 rounded-xl" />
                  <div className="relative p-5 bg-slate-800/60 backdrop-blur-sm border border-slate-700/60 rounded-xl hover:border-purple-500/30 transition-all duration-300">
                    {link.isEditing ? (
                      <div className="space-y-4">
                        {/* Header for edit mode */}
                        <div className="flex items-center gap-2 pb-3 border-b border-slate-700/50">
                          <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${link.color_gradient} animate-pulse`}></div>
                          <span className="text-sm font-medium text-purple-300 font-mono">
                            Editing Store Link #{index + 1}
                          </span>
                        </div>

                        <div className="space-y-3">
                          {/* Row 1: Store Platform + Store URL */}
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div>
                              <Label className="text-xs text-slate-300 font-mono mb-1 block">Store Platform</Label>
                              <Select
                                value={link.store_name}
                                onValueChange={(value) => updateStoreLink(index, 'store_name', value)}
                              >
                                <SelectTrigger className="bg-slate-900/80 border-slate-600/50 text-white hover:border-purple-400/50 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-all duration-200">
                                  <SelectValue placeholder="Choose platform..." />
                                </SelectTrigger>
                                <SelectContent className="bg-slate-900 border-slate-700">
                                  {storeNameSuggestions.map((store) => (
                                    <SelectItem key={store} value={store} className="hover:bg-slate-800">
                                      <div className="flex items-center gap-2">
                                        {getStoreIcon(store)}
                                        <span>{store}</span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label className="text-xs text-slate-300 font-mono mb-1 block">Store URL</Label>
                              <Input
                                placeholder="https://store.steampowered.com/app/..."
                                value={link.store_url}
                                onChange={(e) => updateStoreLink(index, 'store_url', e.target.value)}
                                className="bg-slate-900/80 border-slate-600/50 text-white placeholder:text-slate-500 hover:border-purple-400/50 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-all duration-200"
                              />
                            </div>
                          </div>

                          {/* Row 2: Current Price + Original Price */}
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div>
                              <Label className="text-xs text-slate-300 font-mono mb-1 block">Current Price</Label>
                              <Input
                                placeholder="e.g., $29.99, €24.99, £19.99"
                                value={link.price}
                                onChange={(e) => updateStoreLink(index, 'price', e.target.value)}
                                className="bg-slate-900/80 border-slate-600/50 text-white placeholder:text-slate-500 hover:border-purple-400/50 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-all duration-200"
                              />
                            </div>
                            <div>
                              <Label className="text-xs text-slate-300 font-mono mb-1 block">
                                Original Price <span className="text-slate-500">(Optional)</span>
                              </Label>
                              <Input
                                placeholder="e.g., $39.99 (for discounts)"
                                value={link.original_price || ''}
                                onChange={(e) => updateStoreLink(index, 'original_price', e.target.value)}
                                className="bg-slate-900/80 border-slate-600/50 text-white placeholder:text-slate-500 hover:border-purple-400/50 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-all duration-200"
                              />
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex gap-2 pt-3">
                          <Button
                            onClick={() => toggleStoreEditMode(index)}
                            size="sm"
                            className="flex-1 bg-green-600/80 hover:bg-green-600 text-white border-0 font-medium transition-colors duration-200"
                          >
                            <Check className="h-4 w-4 mr-1" />
                            Save
                          </Button>
                          <Button
                            onClick={() => removeStoreLink(index)}
                            size="sm"
                            variant="outline"
                            className="px-3 text-red-400 border-red-400/50 hover:bg-red-400/10 hover:border-red-400 transition-all duration-200"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-2">
                            {getStoreIcon(link.store_name)}
                            <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${link.color_gradient} shadow-lg`}></div>
                          </div>
                          <div className="space-y-1">
                            <div className="flex items-center gap-3">
                              <span className="text-white font-semibold font-mono text-base">{link.store_name}</span>
                              <div className="flex items-center gap-2">
                                <span className="text-purple-400 font-bold font-mono text-lg">{link.price}</span>
                                {link.original_price && (
                                  <>
                                    <span className="text-slate-500 line-through text-sm font-mono">{link.original_price}</span>
                                    <Badge variant="secondary" className="text-xs bg-green-600/80 text-green-100 px-2 py-0.5">
                                      Sale
                                    </Badge>
                                  </>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <ExternalLink className="h-3 w-3 text-slate-400" />
                              <span className="text-xs text-slate-400 font-mono truncate max-w-xs">
                                {link.store_url}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <Button
                            onClick={() => toggleStoreEditMode(index)}
                            size="sm"
                            variant="ghost"
                            className="text-slate-400 hover:text-purple-400 hover:bg-purple-400/10 transition-all duration-200"
                          >
                            <Edit3 className="h-4 w-4" />
                          </Button>
                          <Button
                            onClick={() => removeStoreLink(index)}
                            size="sm"
                            variant="ghost"
                            className="text-slate-400 hover:text-red-400 hover:bg-red-400/10 transition-all duration-200"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Add Store Link Button */}
            {storeLinks.length < 3 && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-4"
              >
                <Button
                  onClick={addStoreLink}
                  variant="outline"
                  className="w-full h-12 border-2 border-dashed border-purple-500/50 text-purple-400 hover:bg-purple-500/10 hover:border-purple-400 transition-all duration-300 group"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center group-hover:bg-purple-500/30 transition-colors duration-200">
                      <ShoppingCart className="h-3 w-3" />
                    </div>
                    <span className="font-medium">Add Store Link</span>
                    <Badge variant="secondary" className="text-xs bg-purple-600/20 text-purple-300 border border-purple-500/30">
                      {storeLinks.length}/3
                    </Badge>
                  </div>
                </Button>
              </motion.div>
            )}

            {/* Save All Store Links Button */}
            {storeLinks.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-6 pt-4 border-t border-slate-700/50"
              >
                <Button
                  onClick={saveStoreLinks}
                  disabled={isSavingStoreLinks}
                  className="w-full bg-purple-600/80 hover:bg-purple-600 text-white font-medium transition-colors duration-200"
                >
                  {isSavingStoreLinks ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Saving Links...</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Save className="h-4 w-4" />
                      <span>Save All Store Links</span>
                      <Badge variant="secondary" className="text-xs bg-white/20 text-white border-0">
                        {storeLinks.length}
                      </Badge>
                    </div>
                  )}
                </Button>
              </motion.div>
            )}
          </div>

              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </div>
  );
};

export default FeaturedBannerConfig;